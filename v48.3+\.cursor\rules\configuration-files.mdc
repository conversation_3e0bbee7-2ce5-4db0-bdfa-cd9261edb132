---
description:
globs:
alwaysApply: false
---
# 配置文件说明

本项目包含以下主要配置文件：

- **[agents.json](mdc:agents.json)**：定义可用的智能体（Agent）及其属性。
- **[api_templates.json](mdc:api_templates.json)**：API调用模板配置，便于统一和复用API请求结构。
- **[models_config.py](mdc:models_config.py)**：Python格式的模型与API相关配置，包含模型列表、API参数等。
- **[system_prompt_templates.json](mdc:system_prompt_templates.json)**：系统提示词模板，支持多场景下的系统消息定制。
- **[system_prompts.json](mdc:system_prompts.json)**：具体的系统提示词内容，供对话或功能调用。

> 这些配置文件有助于灵活扩展智能体、API和系统提示词，建议修改时注意格式和字段含义。
