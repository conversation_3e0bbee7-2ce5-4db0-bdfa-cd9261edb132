---
description:
globs:
alwaysApply: false
---
# [static/js/main.js](mdc:static/js/main.js) 结构与功能说明

本文件为前端主逻辑脚本，包含以下主要功能模块：

- **日志系统**：Logger对象，支持多级别日志、生产/开发环境切换。
- **DOM缓存与状态管理**：高效缓存DOM元素、管理定时器、事件源、音频等。
- **UI交互**：
  - 事件监听与处理（如按钮、侧边栏、滚动、输入等）
  - 自定义选择器、主题切换、消息搜索、弹窗等
- **会话与消息管理**：
  - 会话的创建、切换、删除、保存与加载
  - 聊天消息的发送、编辑、删除、导入导出、历史记录管理
- **TTS（文本转语音）与音频聊天**：
  - 支持多音色TTS播放、自动TTS队列、录音UI模拟
- **图片生成与预览**：
  - 图片生成参数选择、预览、保存、尺寸调整等
- **Markdown与代码块渲染**：
  - 支持Mermaid、SVG、代码高亮、图形内容即时渲染
- **表单与配置管理**：
  - API配置、模型选择、模板管理、表单校验
- **性能与工具函数**：
  - 节流、防抖、重试、Toast提示、SVG主题适配等

> 该文件体量较大，建议通过搜索函数名或功能关键字快速定位所需模块。
