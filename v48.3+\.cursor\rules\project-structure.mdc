---
description:
globs:
alwaysApply: false
---
# 项目结构说明

本项目的主要结构如下：

- **[app.py](mdc:app.py)**：后端主入口，负责API和服务逻辑。
- **[Index.html](mdc:Index.html)**：前端主页面，用户界面入口。
- **[static/](mdc:static/)**：静态资源目录，包含CSS、JS、图片等前端资源。
  - **[static/css/dark_theme.css](mdc:static/css/dark_theme.css)**：暗色主题样式表。
  - **[static/js/main.js](mdc:static/js/main.js)**：前端主逻辑脚本，包含大量UI与功能实现。
  - **[static/logo.svg](mdc:static/logo.svg)**：项目Logo。
- **[models_config.py](mdc:models_config.py)**：模型与API相关配置。
- **[agents.json](mdc:agents.json)**、**[api_templates.json](mdc:api_templates.json)**、**[system_prompt_templates.json](mdc:system_prompt_templates.json)**、**[system_prompts.json](mdc:system_prompts.json)**：各类配置与模板文件。
- **[requirements.txt](mdc:requirements.txt)**：Python依赖列表。
- **[error.html](mdc:error.html)**：错误页面模板。
- **[logs/](mdc:logs/)**：日志文件目录。

> 详细的功能实现主要集中在[static/js/main.js](mdc:static/js/main.js)中，涉及UI交互、会话管理、TTS、图片生成等。
