# ===========================================
# Python 运行时文件
# ===========================================

# Python 字节码缓存
__pycache__/
*.py[cod]
*$py.class

# 编译的 Python 文件
*.pyc
*.pyo
*.pyd

# Python 包分发文件
*.so
*.egg
*.egg-info/
dist/
build/
*.whl

# ===========================================
# 应用程序运行时文件
# ===========================================

# 日志文件
*.log
*.log.*
logs/
log/

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（包含敏感信息）
.env
.env.local
.env.*.local
config.ini
secrets.json

# ===========================================
# 开发工具文件
# ===========================================

# IDE 配置文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace
.spyderproject
.spyproject

# Jupyter Notebook
.ipynb_checkpoints/
*.ipynb

# ===========================================
# 系统文件
# ===========================================

# Windows 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS 系统文件
.DS_Store
.AppleDouble
.LSOverride
._*

# Linux 系统文件
*~
.directory

# ===========================================
# Web 应用运行时文件
# ===========================================

# Flask 会话文件
flask_session/
instance/

# 上传文件目录
uploads/
upload/
files/

# 缓存目录
cache/
.cache/

# 静态文件编译输出（如果有构建过程）
static/dist/
static/build/

# ===========================================
# 版本控制和部署
# ===========================================

# Git 相关
.git/
.gitattributes

# 部署相关
.deploy/
deploy/
*.pid

# ===========================================
# 其他运行时文件
# ===========================================

# 进程 ID 文件
*.pid

# 锁文件
*.lock

# 备份文件
*.bak
*.backup
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 测试覆盖率报告
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.pytest_cache/