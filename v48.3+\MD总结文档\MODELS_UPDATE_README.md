# Pollinations AI 模型列表优化机制

## 概述

本项目已实现了对Pollinations AI模型列表的优化加载机制，遵循"优先本地加载，然后网络更新"的原则。

## 核心特性

### 1. 优先本地配置
- 系统首先加载 `models_config.py` 中预定义的本地模型列表
- 本地配置包含完整的模型信息（能力、提供商、模态等）
- 确保即使网络不可用时，系统仍能正常工作

### 2. 智能网络更新
- 后台异步从 Pollinations AI API 获取最新模型列表
- 根据 API Cheatsheet.txt 中的端点进行数据获取：
  - 文本模型：`GET https://text.pollinations.ai/models`
  - 图像模型：`GET https://image.pollinations.ai/models`
  - 音频语音：从文本模型API中提取

### 3. 智能合并机制
- 保留本地配置的详细信息和能力定义
- 添加网络API中发现的新模型
- 避免重复，优先使用本地配置

## API 端点

### 基础模型获取
- `GET /api/models/text` - 获取文本模型列表
- `GET /api/models/image` - 获取图像模型列表  
- `GET /api/models/audio_voices` - 获取音频语音列表

### 高级功能
- `GET /api/models/text?force_update=true` - 强制从网络更新文本模型
- `POST /api/models/force_update` - 强制更新所有模型列表
- `GET /api/models/status` - 获取模型状态信息
- `POST /api/models/clear_cache` - 清除模型缓存

## 配置参数

### 更新间隔
- 默认：3600秒（1小时）
- 可通过 `set_update_interval(seconds)` 函数调整

### 网络超时
- API请求超时：10秒
- 确保不会阻塞用户界面

## 使用示例

### Python代码
```python
from models_config import get_text_models, force_update_models

# 获取文本模型（自动后台更新）
models = get_text_models()

# 强制立即更新
result = force_update_models()
if result['success']:
    print("模型更新成功")
```

### API调用
```bash
# 获取文本模型
curl http://localhost:5000/api/models/text

# 强制更新所有模型
curl -X POST http://localhost:5000/api/models/force_update

# 查看模型状态
curl http://localhost:5000/api/models/status
```

## 测试验证

运行测试脚本验证功能：
```bash
python test_models.py
```

测试结果示例：
- ✓ 成功获取 22 个文本模型
- ✓ 成功获取 11 个图像模型  
- ✓ 成功获取 44 个音频语音
- ✓ 本地模型数量: {'text': 22, 'image': 9, 'audio': 19}
- ✓ 网络模型数量: {'text': 0, 'image': 4, 'audio': 25}
- ✓ 合并模型数量: {'text': 22, 'image': 13, 'audio': 44}

## 技术实现

### 核心函数
- `get_text_models(force_update=False)` - 获取文本模型
- `get_image_models(force_update=False)` - 获取图像模型
- `get_audio_voices(force_update=False)` - 获取音频语音
- `_update_models_from_network()` - 网络更新逻辑
- `_merge_models()` - 模型合并逻辑

### 缓存机制
- 内存缓存网络获取的模型数据
- 能力查询缓存，避免重复计算
- 线程安全的更新锁机制

### 错误处理
- 网络请求异常自动降级到本地配置
- JSON解析错误处理
- 超时保护机制

## 优势

1. **可靠性**：本地配置确保基础功能始终可用
2. **实时性**：后台更新保持模型列表最新
3. **性能**：异步更新不阻塞用户操作
4. **扩展性**：易于添加新的模型源和类型
5. **兼容性**：保持现有API接口不变

## 注意事项

- 首次启动时会进行后台网络更新
- 网络更新失败不影响系统正常运行
- 建议在生产环境中定期检查模型状态
- 可根据需要调整更新间隔和超时设置
