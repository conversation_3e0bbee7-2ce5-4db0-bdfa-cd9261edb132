<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏按钮测试页面</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin: 24px 0;
            padding: 16px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        
        .button-demo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 12px 0;
            padding: 12px;
            background: #f9fafb;
            border-radius: 6px;
        }
        
        .demo-toolbar {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: linear-gradient(180deg, rgba(249, 250, 251, 0.8) 0%, rgba(243, 244, 246, 0.6) 100%);
            border: 1px solid #e5e7eb;
            border-radius: 24px;
            backdrop-filter: blur(8px);
        }
        
        /* 复制主应用的按钮样式 */
        .demo-button {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 8px;
            background: transparent;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            font-size: 16px;
            position: relative;
        }
        
        .demo-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #f3f4f6;
            border-radius: inherit;
            opacity: 0;
            transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
            z-index: -1;
        }
        
        .demo-button:hover {
            color: #374151;
            transform: translateY(-1px) scale(1.02);
        }
        
        .demo-button:hover::before {
            opacity: 1;
        }
        
        /* 开关按钮样式 */
        .demo-toggle {
            width: 36px;
            height: 36px;
            border: none;
            border-radius: 8px;
            background: transparent;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1), transform 0.15s cubic-bezier(0, 0, 0.2, 1);
            font-size: 16px;
        }
        
        .demo-toggle:hover {
            transform: translateY(-1px) scale(1.05);
        }
        
        .demo-toggle[data-enabled="false"] {
            color: #6b7280;
        }
        
        .demo-toggle[data-enabled="true"] {
            color: #3b82f6;
        }
        
        .demo-toggle.tts-button[data-enabled="true"] {
            color: #f59e0b;
        }
        
        .demo-toggle.search-button[data-enabled="true"] {
            color: #3b82f6;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .status-on {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-off {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .test-controls {
            margin: 16px 0;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .test-btn:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }
        
        .color-demo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 8px 0;
        }
        
        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }
    </style>
</head>
<body>
    <h1>🧪 工具栏按钮测试页面</h1>
    
    <div class="test-container">
        <h2>📋 修复验证清单</h2>
        <div class="test-section">
            <h3>✅ 按钮图标显示测试</h3>
            <p>测试所有功能按钮在悬停时图标是否保持可见：</p>
            
            <div class="demo-toolbar">
                <button class="demo-button" title="文件上传">
                    <i class="fas fa-paperclip"></i>
                </button>
                <button class="demo-button" title="语音功能">
                    <i class="fas fa-microphone"></i>
                </button>
                <button class="demo-button" title="AI工具集">
                    <i class="fas fa-tools"></i>
                </button>
                <button class="demo-button" title="图像生成">
                    <i class="fas fa-palette"></i>
                </button>
            </div>
            
            <p><strong>测试方法</strong>：将鼠标悬停在每个按钮上，确认图标始终可见。</p>
        </div>
        
        <div class="test-section">
            <h3>💡 智能分析图标更新测试</h3>
            <div class="button-demo">
                <span>新图标：</span>
                <button class="demo-toggle tts-button" data-enabled="false" title="智能分析">
                    <i class="fas fa-lightbulb"></i>
                </button>
                <span id="tts-status" class="status-indicator status-off">关闭</span>
            </div>
            
            <div class="test-controls">
                <button class="test-btn" onclick="toggleTTS()">切换智能分析状态</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎨 开关按钮简化设计测试</h3>
            <div class="button-demo">
                <span>智能分析：</span>
                <button class="demo-toggle tts-button" data-enabled="false" title="智能分析">
                    <i class="fas fa-lightbulb"></i>
                </button>
                <span id="tts-status-2" class="status-indicator status-off">关闭</span>
            </div>
            
            <div class="button-demo">
                <span>网络搜索：</span>
                <button class="demo-toggle search-button" data-enabled="false" title="网络搜索">
                    <i class="fas fa-globe"></i>
                </button>
                <span id="search-status" class="status-indicator status-off">关闭</span>
            </div>
            
            <div class="test-controls">
                <button class="test-btn" onclick="toggleAllSwitches()">切换所有开关</button>
                <button class="test-btn" onclick="resetAllSwitches()">重置所有开关</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🌈 颜色主题测试</h3>
            <div class="color-demo">
                <div class="color-swatch" style="background: #6b7280;"></div>
                <span>关闭状态：#6b7280 (灰色)</span>
            </div>
            <div class="color-demo">
                <div class="color-swatch" style="background: #f59e0b;"></div>
                <span>智能分析开启：#f59e0b (橙黄色)</span>
            </div>
            <div class="color-demo">
                <div class="color-swatch" style="background: #3b82f6;"></div>
                <span>网络搜索开启：#3b82f6 (蓝色)</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📱 响应式测试</h3>
            <p>调整浏览器窗口大小，测试按钮在不同屏幕尺寸下的表现：</p>
            <ul>
                <li><strong>桌面端 (>1024px)</strong>：36x36px 按钮</li>
                <li><strong>平板端 (769-1024px)</strong>：34x34px 按钮</li>
                <li><strong>移动端 (≤768px)</strong>：32x32px 按钮，无变换效果</li>
                <li><strong>超小屏 (≤480px)</strong>：28x28px 按钮</li>
            </ul>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔗 主应用测试</h2>
        <p>在主应用中测试所有修复是否正常工作：</p>
        <div class="test-controls">
            <button class="test-btn" onclick="openMainApp()">打开主应用</button>
        </div>
        
        <h3>📝 测试步骤</h3>
        <ol>
            <li>悬停每个功能按钮，确认图标不消失</li>
            <li>点击智能分析按钮，确认灯泡图标和橙黄色主题</li>
            <li>点击网络搜索按钮，确认蓝色主题</li>
            <li>测试按钮的悬停和点击动画</li>
            <li>在不同屏幕尺寸下测试响应式效果</li>
        </ol>
    </div>

    <script>
        function toggleTTS() {
            const button = document.querySelector('.tts-button');
            const status = document.getElementById('tts-status');
            const status2 = document.getElementById('tts-status-2');
            
            const isEnabled = button.getAttribute('data-enabled') === 'true';
            const newState = !isEnabled;
            
            button.setAttribute('data-enabled', newState.toString());
            
            const statusText = newState ? '开启' : '关闭';
            const statusClass = newState ? 'status-on' : 'status-off';
            
            if (status) {
                status.textContent = statusText;
                status.className = `status-indicator ${statusClass}`;
            }
            if (status2) {
                status2.textContent = statusText;
                status2.className = `status-indicator ${statusClass}`;
            }
        }
        
        function toggleAllSwitches() {
            const buttons = document.querySelectorAll('.demo-toggle');
            buttons.forEach(button => {
                const isEnabled = button.getAttribute('data-enabled') === 'true';
                button.setAttribute('data-enabled', (!isEnabled).toString());
            });
            
            updateAllStatus();
        }
        
        function resetAllSwitches() {
            const buttons = document.querySelectorAll('.demo-toggle');
            buttons.forEach(button => {
                button.setAttribute('data-enabled', 'false');
            });
            
            updateAllStatus();
        }
        
        function updateAllStatus() {
            const ttsButton = document.querySelector('.tts-button');
            const searchButton = document.querySelector('.search-button');
            
            const ttsEnabled = ttsButton.getAttribute('data-enabled') === 'true';
            const searchEnabled = searchButton.getAttribute('data-enabled') === 'true';
            
            const ttsStatus = document.getElementById('tts-status-2');
            const searchStatus = document.getElementById('search-status');
            
            if (ttsStatus) {
                ttsStatus.textContent = ttsEnabled ? '开启' : '关闭';
                ttsStatus.className = `status-indicator ${ttsEnabled ? 'status-on' : 'status-off'}`;
            }
            
            if (searchStatus) {
                searchStatus.textContent = searchEnabled ? '开启' : '关闭';
                searchStatus.className = `status-indicator ${searchEnabled ? 'status-on' : 'status-off'}`;
            }
        }
        
        function openMainApp() {
            window.open('http://localhost:5000', '_blank');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 按钮测试页面已加载');
            console.log('请测试所有按钮的悬停效果和状态切换');
        });
    </script>
</body>
</html>
