[{"name": "openai", "description": "OpenAI GPT-4.1-nano", "provider": "Azure", "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "openai-large", "description": "OpenAI GPT-4.1 mini", "provider": "Azure", "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "openai-reasoning", "description": "OpenAI o4-mini", "reasoning": true, "provider": "Azure", "vision": true, "input_modalities": ["text", "image"], "output_modalities": ["text"], "audio": false}, {"name": "qwen-coder", "description": "Qwen 2.5 Coder 32B", "provider": "Scaleway", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "llama", "description": "Llama 3.3 70B", "provider": "Cloudflare", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "llamascout", "description": "Llama 4 Scout 17B", "provider": "Cloudflare", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "mistral", "description": "Mistral Small 3", "provider": "Cloudflare", "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "unity", "description": "Unity Mistral Large", "provider": "Scaleway", "uncensored": true, "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "midijourney", "description": "Midijourney", "provider": "Azure", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "rtist", "description": "<PERSON><PERSON>", "provider": "Azure", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "searchgpt", "description": "SearchGPT", "provider": "Azure", "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "evil", "description": "Evil", "provider": "Scaleway", "uncensored": true, "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "deepseek-reasoning", "description": "DeepSeek-R1 Di<PERSON>ill Q<PERSON> 32B", "reasoning": true, "provider": "Cloudflare", "aliases": "deepseek-r1", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "deepseek-reasoning-large", "description": "DeepSeek R1 - Llama 70B", "reasoning": true, "provider": "Scaleway", "aliases": "deepseek-r1-llama", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "phi", "description": "Phi-4 Instruct", "provider": "Cloudflare", "input_modalities": ["text", "image", "audio"], "output_modalities": ["text"], "vision": true, "audio": true}, {"name": "llama-vision", "description": "Llama 3.2 11B Vision", "provider": "Cloudflare", "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "gemini", "description": "gemini-2.5-flash-preview-04-17", "provider": "Azure", "input_modalities": ["text", "image", "audio"], "output_modalities": ["audio", "text"], "vision": true, "audio": true}, {"name": "hormoz", "description": "Hormoz 8b", "provider": "Modal", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "hypnosis-tracy", "description": "Hypnosis Tracy 7B", "provider": "Azure", "input_modalities": ["text", "audio"], "output_modalities": ["audio", "text"], "vision": false, "audio": true}, {"name": "deepseek", "description": "DeepSeek-V3", "provider": "DeepSeek", "input_modalities": ["text"], "output_modalities": ["text"], "vision": false, "audio": false}, {"name": "sur", "description": "Sur AI Assistant (Mistral)", "provider": "Scaleway", "input_modalities": ["text", "image"], "output_modalities": ["text"], "vision": true, "audio": false}, {"name": "openai-audio", "description": "OpenAI GPT-4o-audio-preview", "voices": ["alloy", "echo", "fable", "onyx", "nova", "shimmer", "coral", "verse", "ballad", "ash", "sage", "amuch", "dan"], "provider": "Azure", "input_modalities": ["text", "image", "audio"], "output_modalities": ["audio", "text"], "vision": true, "audio": true}]