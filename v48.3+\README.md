# LuckyStar AI聊天助手

<div align="center">
  <img src="static/logo.svg" alt="LuckyStar Logo" width="120" height="120">
  
  **智能对话 · 图像生成 · 语音交互**
  
  一个功能丰富的AI聊天助手，集成多模态AI能力，提供流畅的用户体验
  
  [![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
  [![Flask](https://img.shields.io/badge/Flask-3.0.3-green.svg)](https://flask.palletsprojects.com)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## ✨ 核心特性

### 🤖 智能对话系统
- **多模型支持**：集成30+AI模型，包括GPT-4、Llama、Mistral、DeepSeek等
- **流式输出**：实时显示AI回复，支持思考过程可视化
- **多模态输入**：支持文本、图像、音频输入
- **会话管理**：本地存储、导入导出、历史记录

### 🎨 图像生成功能
- **多种生成模式**：单张生成、批量生成（1-16张）
- **丰富的模型选择**：Flux、Flux Pro、Flux Realism、Flux Anime等
- **智能参数系统**：精确宽高比计算、自定义尺寸
- **高级功能**：负面提示词、种子值控制、提示词优化

### 🎵 智能语音分析
- **智能分析播报**：18种音色选择，AI对消息内容进行深度分析并语音播报见解和建议

### 🎯 用户体验
- **响应式设计**：完美适配桌面和移动设备
- **暗黑主题**：护眼的深色模式
- **代码渲染**：支持Mermaid图表、语法高亮
- **实时搜索**：快速查找历史消息

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd LuckyStar
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动应用**
```bash
python app.py
```

4. **访问应用**
打开浏览器访问：`http://localhost:5000`

### 配置说明

项目支持环境变量配置，主要配置项：

```bash
# 服务器配置
HOST=0.0.0.0
PORT=5000
FLASK_DEBUG=false

# API端点配置
POLLINATIONS_TEXT_ENDPOINT=https://text.pollinations.ai/
POLLINATIONS_IMAGE_ENDPOINT=https://image.pollinations.ai/prompt/
POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT=https://text.pollinations.ai/openai

# 默认设置
DEFAULT_SYSTEM_PROMPT=""
MAX_TOKENS_DEFAULT=0
```

## 📁 项目结构

```
LuckyStar/
├── app.py                          # Flask后端主程序
├── Index.html                      # 前端主页面
├── models_config.py                # AI模型配置
├── requirements.txt                # Python依赖
├── static/                         # 静态资源
│   ├── css/
│   │   └── dark_theme.css         # 暗黑主题样式
│   ├── js/
│   │   └── main.js                # 前端主逻辑
│   ├── logo.svg                   # 项目Logo
│   └── favicon.ico                # 网站图标
├── logs/                          # 日志文件
├── error.html                     # 错误页面
├── agents.json                    # AI代理配置
├── api_templates.json             # API模板配置
├── system_prompt_templates.json   # 系统提示词模板
├── system_prompts.json            # 系统提示词
├── 绘画模块综合说明文档.md         # 绘画功能详细文档
└── 项目问题解决方案合集.md         # 开发问题解决方案
```

## 🔧 技术架构

### 后端技术栈
- **Flask 3.0.3**：Web框架核心
- **Requests 2.32.3**：HTTP请求处理
- **Werkzeug 3.0.4**：WSGI工具
- **Jinja2 3.1.4**：模板引擎

### 前端技术栈
- **原生JavaScript**：核心逻辑实现
- **CSS3**：响应式设计和主题系统
- **Marked.js**：Markdown渲染
- **Mermaid.js**：图表渲染
- **Highlight.js**：代码语法高亮

### API集成
- **Pollinations.ai**：主要AI服务提供商
  - 文本生成：`https://text.pollinations.ai/`
  - 图像生成：`https://image.pollinations.ai/`
  - 语音合成：支持18种音色
- **兼容OpenAI API**：支持第三方API接入

## 🎮 使用指南

### 基础对话
1. 在输入框输入问题
2. 选择AI模型（可选）
3. 点击发送或按Enter键
4. 查看AI实时回复

### 图像生成
1. 点击"绘画"按钮打开绘画模块
2. 输入图像描述
3. 选择模型和参数（尺寸、比例等）
4. 点击"单张生成"或"批量生成"
5. 查看和保存生成的图像

### 智能语音分析
- **智能分析播报**：点击消息旁的智能分析按钮，AI会对内容进行深度分析并语音播报见解和建议

### 会话管理
- **新建会话**：点击侧边栏的"+"按钮
- **切换会话**：点击侧边栏的会话标题
- **导出会话**：在设置中选择导出格式
- **导入会话**：拖拽文件到页面或使用导入功能

## 🛠️ 开发指南

### 本地开发
```bash
# 开启调试模式
export FLASK_DEBUG=true
python app.py
```

### 代码结构
- `app.py`：包含所有API端点和业务逻辑
- `static/js/main.js`：前端核心功能实现
- `models_config.py`：AI模型和配置管理

### 添加新功能
1. 后端API：在`app.py`中添加新的路由
2. 前端逻辑：在`main.js`中实现对应功能
3. 样式设计：在CSS文件中添加样式

## 📚 文档资源

- [绘画模块详细文档](绘画模块综合说明文档.md)
- [项目问题解决方案](项目问题解决方案合集.md)
- [Pollinations.ai API文档](Pollination.ai%20项目集成指南.txt)

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看[LICENSE](LICENSE)文件了解详情

## 🙏 致谢

- [Pollinations.ai](https://pollinations.ai) - AI服务提供
- [Flask](https://flask.palletsprojects.com) - Web框架
- [Marked.js](https://marked.js.org) - Markdown解析
- [Mermaid.js](https://mermaid-js.github.io) - 图表渲染

---

<div align="center">
  Made with ❤️ by LuckyStar Team
</div>
