@echo off
REM Activate Python virtual environment batch script
REM This script bypasses PowerShell execution policy issues

echo Activating Python virtual environment...
echo.

REM Check if virtual environment exists
if not exist ".venv\Scripts\activate.bat" (
    echo Error: Virtual environment does not exist!
    echo Please ensure you run this script in the project root directory
    pause
    exit /b 1
)

REM Activate virtual environment
call .venv\Scripts\activate.bat

REM Display Python version and path
echo.
echo Virtual environment activated successfully!
echo Python version:
python --version
echo.
echo Python path:
where python
echo.
echo Installed packages:
pip list --format=columns
echo.
echo To exit virtual environment, type: deactivate
echo.

REM Start new command prompt session with virtual environment active
cmd /k
