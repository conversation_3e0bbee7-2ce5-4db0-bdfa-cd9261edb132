# PowerShell虚拟环境激活脚本
# 包含执行策略检查和自动修复

param(
    [switch]$FixPolicy = $false
)

Write-Host "=== Python虚拟环境激活脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查虚拟环境是否存在
if (-not (Test-Path ".venv\Scripts\Activate.ps1")) {
    Write-Host "错误：虚拟环境不存在！" -ForegroundColor Red
    Write-Host "请先创建虚拟环境：python -m venv .venv" -ForegroundColor Yellow
    exit 1
}

# 检查当前执行策略
$currentPolicy = Get-ExecutionPolicy
Write-Host "当前执行策略：$currentPolicy" -ForegroundColor Cyan

if ($currentPolicy -eq "Restricted") {
    Write-Host "检测到受限的执行策略，需要修改以运行脚本" -ForegroundColor Yellow
    
    if ($FixPolicy) {
        Write-Host "正在设置执行策略为RemoteSigned（仅当前用户）..." -ForegroundColor Yellow
        try {
            Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            Write-Host "执行策略已成功更新！" -ForegroundColor Green
        }
        catch {
            Write-Host "无法更新执行策略：$($_.Exception.Message)" -ForegroundColor Red
            Write-Host "请以管理员身份运行PowerShell，或使用批处理脚本替代" -ForegroundColor Yellow
            exit 1
        }
    }
    else {
        Write-Host "请运行以下命令之一来修复执行策略：" -ForegroundColor Yellow
        Write-Host "1. .\activate_powershell.ps1 -FixPolicy" -ForegroundColor Cyan
        Write-Host "2. Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor Cyan
        Write-Host "或者使用批处理脚本：activate_env.bat" -ForegroundColor Cyan
        exit 1
    }
}

# 激活虚拟环境
Write-Host "正在激活虚拟环境..." -ForegroundColor Green
try {
    & ".venv\Scripts\Activate.ps1"
    Write-Host ""
    Write-Host "虚拟环境已成功激活！" -ForegroundColor Green
    Write-Host ""
    
    # 显示环境信息
    Write-Host "Python版本：" -ForegroundColor Cyan
    python --version
    Write-Host ""
    
    Write-Host "Python路径：" -ForegroundColor Cyan
    Get-Command python | Select-Object -ExpandProperty Source
    Write-Host ""
    
    Write-Host "已安装的包：" -ForegroundColor Cyan
    pip list --format=columns
    Write-Host ""
    
    Write-Host "要退出虚拟环境，请输入：deactivate" -ForegroundColor Yellow
}
catch {
    Write-Host "激活虚拟环境时出错：$($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请尝试使用批处理脚本：activate_env.bat" -ForegroundColor Yellow
    exit 1
}
