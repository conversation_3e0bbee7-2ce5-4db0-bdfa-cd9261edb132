[{"name": "Chutes API", "endpoint": "https://llm.chutes.ai/v1/chat/completions", "models": ["chutesai/Llama-4-Maverick-17B-128E-Instruct-FP8", "chutesai/Mistral-Small-3.1-24B-Instruct-2503", "deepseek-ai/DeepSeek-R1", "deepseek-ai/DeepSeek-R1-Zero", "deepseek-ai/DeepSeek-V3-0324", "Qwen/Qwen2.5-VL-32B-Instruct", "unsloth/gemma-3-12b-it"]}, {"name": "API易", "endpoint": "https://vip.apiyi.com/v1/chat/completions", "models": ["chatgpt-4o-latest", "dall-e-3", "deepseek-r1", "deepseek-v3-250324", "gemini-2.0-flash-exp", "gemini-2.0-flash-exp-image-generation", "gemini-2.5-pro-exp-03-25", "grok-3-imageGen", "grok-3-latest"]}, {"name": "Gemini API", "endpoint": "https://generativelanguage.googleapis.com/v1beta/models/", "models": ["gemini-2.0-flash-exp", "gemini-2.5-pro-exp-03-25"]}, {"name": "OpenRouter", "endpoint": "https://openrouter.ai/api/v1/chat/completions", "models": ["cognitivecomputations/dolphin3.0-mistral-24b:free", "cognitivecomputations/dolphin3.0-r1-mistral-24b:free", "deepseek/deepseek-chat-v3-0324:free", "deepseek/deepseek-chat:free", "deepseek/deepseek-r1-distill-llama-70b:free", "deepseek/deepseek-r1-distill-qwen-32b:free", "deepseek/deepseek-r1-zero:free", "deepseek/deepseek-r1:free", "google/gemini-2.0-flash-exp:free", "google/gemini-2.0-flash-lite-preview-02-05:free", "google/gemini-2.0-flash-thinking-exp-1219:free", "google/gemini-2.0-flash-thinking-exp:free", "google/gemini-2.0-pro-exp-02-05:free", "google/gemini-2.5-pro-exp-03-25:free", "google/gemini-exp-1206:free", "google/gemini-flash-1.5-8b-exp", "google/gemma-3-12b-it:free", "google/gemma-3-27b-it:free", "gryphe/mythomax-l2-13b:free", "meta-llama/llama-3.1-8b-instruct:free", "mistralai/mistral-nemo:free", "mistralai/mistral-small-24b-instruct-2501:free", "mistralai/mistral-small-3.1-24b-instruct:free", "moonshotai/moonlight-16b-a3b-instruct:free", "nousresearch/deephermes-3-llama-3-8b-preview:free", "open-r1/olympiccoder-32b:free", "qwen/qwq-32b:free", "qwen/qwen2.5-vl-72b-instruct:free", "rekaai/reka-flash-3:free", "sophosympatheia/rogue-rose-103b-v0.2:free"]}, {"name": "天翼云", "endpoint": "https://wishub-x1.ctyun.cn/v1/chat/completions", "models": ["40f9ae16e840417289ad2951f5b2c88f", "4bd107bff85941239e27b1509eccfe98", "7ba7726dad4c4ea4ab7f39c7741aea68", "9dc913a037774fc0b248376905c85da5"]}, {"name": "Pollinations ai", "endpoint": "https://text.pollinations.ai/openai", "models": ["openai", "openai-large", "openai-reasoning", "qwen-coder", "llama", "llamascout", "mistral", "unity", "midijourney", "rtist", "searchgpt", "evil", "deepseek-reasoning", "deepseek-reasoning-large", "phi", "llama-vision", "gemini", "hormoz", "hypnosis-tracy", "deepseek", "sur", "openai-audio"]}]