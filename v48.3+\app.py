# app.py
import os
import requests
import logging
import json
import urllib.parse
import re # Needed for regex in log filtering
import time
import threading
from collections import defaultdict, deque
from flask import Flask, render_template, request, Response, stream_with_context, jsonify, send_from_directory, make_response
from jinja2 import TemplateNotFound

from datetime import datetime # 用于生成时间戳
# 统一导入配置和模型数据
from models_config import (
    get_text_models, get_image_models, get_audio_voices,
    force_update_models, get_models_status, clear_models_cache
) # 从新文件中导入

# --- 自定义日志过滤器，用于脱敏 API 密钥 ---
class WerkzeugAPIFilter(logging.Filter):
    def __init__(self, name=''):
        super().__init__(name)
        self.api_key_pattern = re.compile(
            r"""(["']?api_key["']?\s*[:=]\s*["']?)(cpk_[a-zA-Z0-9_.-]+|sk-[a-zA-Z0-9_.-]+)(["']?)""",
            re.VERBOSE
        )
        self.data_param_pattern = re.compile(r"(data=)([^&]+)")

    def _sanitize_data_value(self, encoded_data_val):
        try:
            decoded_data_val = urllib.parse.unquote(encoded_data_val)
            try:
                data_obj = json.loads(decoded_data_val)
                if isinstance(data_obj, dict) and "api_key" in data_obj:
                    api_key_val = data_obj["api_key"]
                    if isinstance(api_key_val, str) and (api_key_val.startswith("cpk_") or api_key_val.startswith("sk-")):
                        data_obj["api_key"] = "***REDACTED_API_KEY***"
                        return urllib.parse.quote(json.dumps(data_obj))
            except json.JSONDecodeError:
                pass
            
            sanitized_decoded_data = self.api_key_pattern.sub(r'\1***REDACTED_API_KEY***\3', decoded_data_val)
            if sanitized_decoded_data != decoded_data_val:
                 return urllib.parse.quote(sanitized_decoded_data)
            return encoded_data_val 
        except Exception:
            return encoded_data_val

    def _sanitize_request_line(self, request_line):
        if not isinstance(request_line, str):
            return request_line
        
        def replace_data_param(match):
            prefix = match.group(1)
            encoded_val = match.group(2)
            return prefix + self._sanitize_data_value(encoded_val)

        new_request_line = self.data_param_pattern.sub(replace_data_param, request_line)
        
        final_sanitized_line = self.api_key_pattern.sub(r'\1***REDACTED_API_KEY***\3', new_request_line)
        return final_sanitized_line

    def filter(self, record):
        if record.name == 'werkzeug':
            sanitized_in_args = False
            if hasattr(record, 'args') and isinstance(record.args, tuple):
                new_args = list(record.args)
                changed_in_current_args = False
                for i, arg_val in enumerate(new_args):
                    if isinstance(arg_val, str) and ("GET " in arg_val or "POST " in arg_val or "PUT " in arg_val or "DELETE " in arg_val) and " HTTP/" in arg_val:
                        original_arg = arg_val
                        new_args[i] = self._sanitize_request_line(original_arg)
                        if new_args[i] != original_arg:
                            changed_in_current_args = True
                if changed_in_current_args:
                    record.args = tuple(new_args)
                    sanitized_in_args = True

            if hasattr(record, 'msg') and isinstance(record.msg, str):
                is_likely_format_string = '%' in record.msg and record.args is not None and len(record.args) > 0
                if not (sanitized_in_args and is_likely_format_string):
                    original_msg = record.msg
                    if ("GET " in original_msg or "POST " in original_msg) and " HTTP/" in original_msg:
                        new_msg = self._sanitize_request_line(original_msg)
                        if new_msg != original_msg:
                            record.msg = new_msg
        return True

# --- 统一配置管理 ---
class AppConfig:
    """应用配置管理类，统一处理所有配置项"""

    def __init__(self):
        # 基础配置
        self.DEBUG_MODE = self._get_bool_env("FLASK_DEBUG", False)
        self.HOST = self._get_str_env("HOST", "0.0.0.0")
        self.PORT = self._get_int_env("PORT", 5000, min_value=1, max_value=65535)

        # 应用配置
        self.DEFAULT_SYSTEM_PROMPT = self._get_str_env("DEFAULT_SYSTEM_PROMPT", "")
        self.API_TEMPLATES_FILE = self._get_str_env("API_TEMPLATES_FILE", "api_templates.json")
        self.MAX_TOKENS_DEFAULT = self._get_int_env("MAX_TOKENS_DEFAULT", 0, min_value=0)

        # API端点配置
        self.POLLINATIONS_TEXT_ENDPOINT = self._get_str_env(
            "POLLINATIONS_TEXT_ENDPOINT",
            "https://text.pollinations.ai/"
        )
        self.POLLINATIONS_IMAGE_ENDPOINT = self._get_str_env(
            "POLLINATIONS_IMAGE_ENDPOINT",
            "https://image.pollinations.ai/prompt/"
        )
        self.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT = self._get_str_env(
            "POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT",
            "https://text.pollinations.ai/openai"
        )
        self.POLLINATIONS_TTS_ENDPOINT = self._get_str_env(
            "POLLINATIONS_TTS_ENDPOINT",
            "https://text.pollinations.ai/openai"
        )

    def _get_str_env(self, key, default=""):
        """获取字符串环境变量"""
        value = os.getenv(key, default)
        return value.strip() if value else default

    def _get_int_env(self, key, default=0, min_value=None, max_value=None):
        """获取整数环境变量"""
        try:
            value = int(os.getenv(key, str(default)))
            if min_value is not None and value < min_value:
                logging.warning(f"Config {key}={value} below minimum {min_value}, using {min_value}")
                return min_value
            if max_value is not None and value > max_value:
                logging.warning(f"Config {key}={value} above maximum {max_value}, using {max_value}")
                return max_value
            return value
        except (ValueError, TypeError):
            logging.warning(f"Invalid integer for {key}, using default {default}")
            return default

    def _get_bool_env(self, key, default=False):
        """获取布尔环境变量"""
        value = os.getenv(key, "").lower()
        if value in ["true", "1", "yes", "on"]:
            return True
        elif value in ["false", "0", "no", "off"]:
            return False
        else:
            return default

    def _validate_config(self):
        """验证配置的有效性"""
        # 验证端口范围
        if not (1 <= self.PORT <= 65535):
            raise ValueError(f"Invalid port number: {self.PORT}")

    def get_summary(self):
        """获取配置摘要"""
        return {
            'debug_mode': self.DEBUG_MODE,
            'host': self.HOST,
            'port': self.PORT,
            'has_default_system_prompt': bool(self.DEFAULT_SYSTEM_PROMPT),
            'max_tokens_default': self.MAX_TOKENS_DEFAULT,
            'api_templates_file': self.API_TEMPLATES_FILE
        }

# 创建配置实例（延迟验证URL直到辅助函数定义后）
config = AppConfig()

# 在辅助函数定义后进行URL验证
def validate_config_urls():
    """验证配置中的URL格式"""
    url_configs = [
        ('POLLINATIONS_TEXT_ENDPOINT', config.POLLINATIONS_TEXT_ENDPOINT),
        ('POLLINATIONS_IMAGE_ENDPOINT', config.POLLINATIONS_IMAGE_ENDPOINT),
        ('POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT', config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT),
        ('POLLINATIONS_TTS_ENDPOINT', config.POLLINATIONS_TTS_ENDPOINT)
    ]

    for name, url in url_configs:
        if not is_valid_url(url):
            logging.warning(f"Invalid URL for {name}: {url}")

# --- 增强的日志配置 ---
from logging.handlers import RotatingFileHandler
import atexit

# 创建日志目录
log_dir = 'logs'
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# 配置轮转日志处理器
log_file = os.path.join(log_dir, 'app.log')
file_handler = RotatingFileHandler(
    log_file,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5,          # 保留5个备份文件
    encoding='utf-8'
)

# 配置日志格式
formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
)
file_handler.setFormatter(formatter)

# 配置控制台处理器
console_handler = logging.StreamHandler()
console_handler.setFormatter(formatter)

# 添加敏感信息过滤器
file_handler.addFilter(WerkzeugAPIFilter())
console_handler.addFilter(WerkzeugAPIFilter())

# 配置根日志器
logging.basicConfig(
    level=logging.INFO if not config.DEBUG_MODE else logging.DEBUG,
    handlers=[file_handler, console_handler]
)

# 日志清理函数
def cleanup_old_logs():
    """清理超过30天的日志文件"""
    try:
        import glob
        log_pattern = os.path.join(log_dir, 'app.log.*')
        current_time = time.time()

        for log_file in glob.glob(log_pattern):
            file_age = current_time - os.path.getmtime(log_file)
            if file_age > 30 * 24 * 3600:  # 30天
                os.remove(log_file)
                logging.info(f"Removed old log file: {log_file}")
    except Exception as e:
        logging.warning(f"Failed to cleanup old logs: {e}")

# 注册退出时的清理函数
atexit.register(cleanup_old_logs)

# 立即执行一次清理
cleanup_old_logs()

# 获取 werkzeug logger 并添加自定义过滤器
werkzeug_logger = logging.getLogger('werkzeug')
if not any(isinstance(f, WerkzeugAPIFilter) for f in werkzeug_logger.filters):
    werkzeug_logger.addFilter(WerkzeugAPIFilter())
werkzeug_logger.setLevel(logging.INFO)
logging.info("--- Application Start ---")
logging.info(f"Debug Mode: {config.DEBUG_MODE}")
logging.info(f"Default SYSTEM_PROMPT: {'Set' if config.DEFAULT_SYSTEM_PROMPT else 'Not Set'}")
logging.info(f"API Templates File Path: {config.API_TEMPLATES_FILE}")
logging.info(f"Pollinations Text Fallback GET Endpoint: {config.POLLINATIONS_TEXT_ENDPOINT}")
logging.info(f"Pollinations Image Endpoint: {config.POLLINATIONS_IMAGE_ENDPOINT}")
logging.info(f"Pollinations OpenAI Compatible POST Endpoint (Text/Vision/TTS): {config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT}")
logging.info(f"Pollinations TTS Endpoint: {config.POLLINATIONS_TTS_ENDPOINT}")
logging.info(f"Default Max Tokens (Backend): {config.MAX_TOKENS_DEFAULT if config.MAX_TOKENS_DEFAULT > 0 else 'Unlimited'}")

# --- Flask 应用初始化 ---
app = Flask(__name__, template_folder='.', static_folder='static', static_url_path='/static')

# --- 安全配置 ---
# 设置安全的会话密钥
def generate_secure_secret_key():
    """生成安全的密钥"""
    import secrets
    return secrets.token_urlsafe(32)

# 获取或生成安全密钥
secret_key = config._get_str_env("SECRET_KEY", None)
if not secret_key or secret_key in ["your-secret-key-change-in-production", "dev-key", "test-key"]:
    if config.DEBUG_MODE:
        # 开发环境使用固定密钥以便调试
        secret_key = "dev-key-" + generate_secure_secret_key()
        logging.info("Using generated secret key for development. Set SECRET_KEY environment variable for production.")
    else:
        # 生产环境必须设置安全密钥
        logging.error("SECRET_KEY not set in production! This is a critical security issue.")
        logging.error("Please set SECRET_KEY environment variable immediately.")
        # 生成临时密钥但记录严重警告
        secret_key = "INSECURE-TEMP-" + generate_secure_secret_key()
        logging.critical("Using INSECURE temporary key. Application may not function correctly.")

app.secret_key = secret_key

# 验证关键配置
def validate_production_config():
    """验证生产环境配置"""
    if not config.DEBUG_MODE:
        warnings = []

        if not config._get_str_env("SECRET_KEY"):
            warnings.append("SECRET_KEY environment variable not set - using temporary key")

        if config.HOST == "0.0.0.0":
            warnings.append("Running on all interfaces (0.0.0.0) in production mode")

        if warnings:
            for warning in warnings:
                logging.warning(f"Production config warning: {warning}")
            logging.warning("Please review production configuration for security")

validate_production_config()

# 安全头设置
@app.after_request
def add_security_headers(response):
    """添加安全HTTP头"""
    # 防止点击劫持
    response.headers['X-Frame-Options'] = 'DENY'

    # 防止MIME类型嗅探
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # XSS保护
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # 引用者策略
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # 权限策略
    response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'

    # 内容安全策略（CSP）
    csp_policy = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' "
        "https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; "
        "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; "
        "font-src 'self' https://cdnjs.cloudflare.com; "
        "img-src 'self' data: blob: https:; "
        "connect-src 'self' https: wss: ws:; "
        "media-src 'self' blob:; "
        "object-src 'none'; "
        "base-uri 'self'; "
        "form-action 'self'"
    )
    response.headers['Content-Security-Policy'] = csp_policy

    # HTTPS强制（生产环境）
    if not config.DEBUG_MODE:
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

    return response

# --- 线程安全的速率限制系统 ---
class RateLimiter:
    def __init__(self):
        self.requests = defaultdict(deque)
        self.blocked_ips = {}
        self._lock = threading.RLock()  # 使用可重入锁

    def is_allowed(self, ip, endpoint, limit=60, window=60, block_duration=300):
        """
        检查IP是否被允许访问（线程安全）
        :param ip: 客户端IP
        :param endpoint: 端点名称
        :param limit: 时间窗口内的请求限制
        :param window: 时间窗口（秒）
        :param block_duration: 封禁时长（秒）
        """
        with self._lock:
            current_time = time.time()
            key = f"{ip}:{endpoint}"

            # 检查是否被封禁
            if ip in self.blocked_ips:
                if current_time - self.blocked_ips[ip] < block_duration:
                    return False, "IP temporarily blocked due to rate limiting"
                else:
                    del self.blocked_ips[ip]

            # 清理过期请求
            request_times = self.requests[key]
            while request_times and current_time - request_times[0] > window:
                request_times.popleft()

            # 检查是否超过限制
            if len(request_times) >= limit:
                self.blocked_ips[ip] = current_time
                logging.warning(f"Rate limit exceeded for IP {ip} on endpoint {endpoint}")
                return False, "Rate limit exceeded"

            # 记录请求
            request_times.append(current_time)
            return True, None

rate_limiter = RateLimiter()

def rate_limit_check(endpoint, limit=60, window=60):
    """速率限制装饰器"""
    def decorator(f):
        def wrapper(*args, **kwargs):
            client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            if client_ip:
                client_ip = client_ip.split(',')[0].strip()

            allowed, message = rate_limiter.is_allowed(client_ip, endpoint, limit, window)
            if not allowed:
                return jsonify({"error": message}), 429

            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

logging.info(f"Template folder path used by Flask: {app.template_folder}")
logging.info(f"Static folder path used by Flask: {app.static_folder}")
logging.info("Security headers configured")
logging.info("Rate limiting system initialized")

# --- 数据验证和清理系统 ---
class DataValidator:
    @staticmethod
    def sanitize_string(value, max_length=None, allow_html=False):
        """清理字符串输入"""
        if not isinstance(value, str):
            return ""

        # 移除控制字符
        value = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)

        # 如果不允许HTML，转义HTML字符
        if not allow_html:
            value = value.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        # 限制长度
        if max_length and len(value) > max_length:
            value = value[:max_length]

        return value.strip()

    @staticmethod
    def validate_json_input(data, required_fields=None, optional_fields=None):
        """验证JSON输入"""
        if not isinstance(data, dict):
            return False, "Invalid JSON format"

        required_fields = required_fields or []
        optional_fields = optional_fields or []
        allowed_fields = set(required_fields + optional_fields)

        # 检查必需字段
        for field in required_fields:
            if field not in data:
                return False, f"Missing required field: {field}"

        # 检查不允许的字段
        for field in data.keys():
            if field not in allowed_fields:
                return False, f"Unexpected field: {field}"

        return True, None

    @staticmethod
    def validate_api_key(api_key):
        """验证API密钥格式"""
        if not isinstance(api_key, str):
            return False

        # 检查常见的API密钥格式
        patterns = [
            r'^sk-[a-zA-Z0-9]{48,}$',  # OpenAI格式
            r'^cpk_[a-zA-Z0-9_.-]+$',  # 自定义格式
            r'^[a-zA-Z0-9]{32,}$'      # 通用格式
        ]

        return any(re.match(pattern, api_key) for pattern in patterns)

data_validator = DataValidator()

# --- 文件系统安全工具 ---
class FileSystemSecurity:
    @staticmethod
    def is_safe_path(path, allowed_dirs=None):
        """检查路径是否安全，防止目录遍历攻击"""
        if not path:
            return False

        # 规范化路径
        try:
            normalized_path = os.path.normpath(path)
            real_path = os.path.realpath(normalized_path)
        except (OSError, ValueError):
            return False

        # 检查是否包含危险字符
        dangerous_patterns = ['..', '~', '$', '|', ';', '&', '`']
        for pattern in dangerous_patterns:
            if pattern in path:
                return False

        # 如果指定了允许的目录，检查路径是否在允许范围内
        if allowed_dirs:
            app_root = os.path.dirname(os.path.abspath(__file__))
            for allowed_dir in allowed_dirs:
                allowed_path = os.path.realpath(os.path.join(app_root, allowed_dir))
                if real_path.startswith(allowed_path):
                    return True
            return False

        return True

    @staticmethod
    def sanitize_filename(filename):
        """清理文件名，移除危险字符"""
        if not filename:
            return ""

        # 移除路径分隔符和危险字符
        dangerous_chars = ['/', '\\', '..', '~', '$', '|', ';', '&', '`', '<', '>', '"', "'", '*', '?', ':']
        sanitized = filename
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '_')

        # 限制文件名长度
        if len(sanitized) > 255:
            sanitized = sanitized[:255]

        return sanitized

    @staticmethod
    def get_safe_file_path(base_dir, filename):
        """获取安全的文件路径"""
        if not FileSystemSecurity.is_safe_path(filename):
            return None

        sanitized_filename = FileSystemSecurity.sanitize_filename(filename)
        if not sanitized_filename:
            return None

        full_path = os.path.join(base_dir, sanitized_filename)

        # 确保最终路径在基础目录内
        try:
            real_base = os.path.realpath(base_dir)
            real_full = os.path.realpath(full_path)
            if not real_full.startswith(real_base):
                return None
        except (OSError, ValueError):
            return None

        return full_path

file_security = FileSystemSecurity()

# --- 错误处理 ---
@app.errorhandler(500)
def internal_server_error(e):
    logging.error(f"Server Error: {e}", exc_info=True)
    error_msg = "服务器内部错误，请查看后端日志。"
    content_type = request.headers.get('Accept', '*/*')
    is_sse_request = 'text/event-stream' in content_type
    is_json_request = 'application/json' in content_type and 'text/html' not in content_type

    if is_sse_request:
        def error_stream(): yield f"event: error\ndata: {json.dumps({'error': error_msg})}\n\n"
        return Response(error_stream(), content_type='text/event-stream; charset=utf-8', status=500)
    elif is_json_request or request.path.startswith('/api/'):
         return jsonify(error=error_msg), 500
    else:
        try: return render_template('error.html', error_message=error_msg), 500
        except Exception: return f"<h1>服务器错误</h1><p>{error_msg}</p>", 500

@app.route('/placeholder/<int:width>x<int:height>')
def generate_placeholder_image(width, height):
    """生成本地占位图片"""
    try:
        # 限制图片尺寸范围
        width = max(50, min(width, 2000))
        height = max(50, min(height, 2000))

        # 创建SVG占位图片
        svg_content = f'''<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ddd" stroke-width="2"/>
            <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16"
                  text-anchor="middle" dominant-baseline="middle" fill="#999">
                {width} × {height}
            </text>
        </svg>'''

        response = make_response(svg_content)
        response.headers['Content-Type'] = 'image/svg+xml'
        response.headers['Cache-Control'] = 'public, max-age=3600'
        return response

    except Exception as e:
        logging.error(f"Error generating placeholder image: {e}")
        return '', 404

@app.errorhandler(404)
def page_not_found(error):
    # 忽略Chrome开发者工具和其他浏览器工具的请求，避免日志污染
    ignored_paths = [
        '/.well-known/appspecific/com.chrome.devtools.json',
        '/.well-known/security.txt',
        '/robots.txt',
        '/sitemap.xml'
    ]

    if request.path in ignored_paths or request.path.startswith('/.well-known/'):
        return '', 404

    # 记录错误信息用于调试
    if config.DEBUG_MODE:
        logging.warning(f"Not Found: {request.path} - {error}")

    content_type = request.headers.get('Accept', '*/*')
    is_json_request = 'application/json' in content_type and 'text/html' not in content_type

    if is_json_request or request.path.startswith('/api/'):
        return jsonify(error="Not Found"), 404
    else:
        try:
            return render_template('error.html', error_message="页面未找到 (404)"), 404
        except (FileNotFoundError, TemplateNotFound):
            return "<h1>页面未找到 (404)</h1>", 404
        except Exception as e:
            logging.error(f"Error rendering 404 page: {e}")
            return "<h1>页面未找到 (404)</h1>", 404

# --- 输入验证和辅助函数 ---
def validate_string_input(value, field_name, max_length=None, min_length=None, required=True):
    """验证字符串输入"""
    if value is None:
        if required:
            raise ValueError(f"{field_name} 不能为空")
        return ""

    if not isinstance(value, str):
        raise ValueError(f"{field_name} 必须是字符串类型")

    value = value.strip()

    if required and not value:
        raise ValueError(f"{field_name} 不能为空")

    if min_length is not None and len(value) < min_length:
        raise ValueError(f"{field_name} 长度不能少于 {min_length} 个字符")

    if max_length is not None and len(value) > max_length:
        raise ValueError(f"{field_name} 长度不能超过 {max_length} 个字符")

    return value

def validate_integer_input(value, field_name, min_value=None, max_value=None, required=True):
    """验证整数输入"""
    if value is None:
        if required:
            raise ValueError(f"{field_name} 不能为空")
        return None

    try:
        if isinstance(value, str):
            value = int(value)
        elif not isinstance(value, int):
            raise ValueError(f"{field_name} 必须是整数类型")
    except (ValueError, TypeError):
        raise ValueError(f"{field_name} 必须是有效的整数")

    if min_value is not None and value < min_value:
        raise ValueError(f"{field_name} 不能小于 {min_value}")

    if max_value is not None and value > max_value:
        raise ValueError(f"{field_name} 不能大于 {max_value}")

    return value

def validate_json_input(data, required_fields=None):
    """验证JSON输入"""
    if not data:
        raise ValueError("请求数据不能为空")

    if not isinstance(data, dict):
        raise ValueError("请求数据必须是有效的JSON对象")

    if required_fields:
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ValueError(f"缺少必需字段: {', '.join(missing_fields)}")

    return data

def format_pollinations_url_component(text):
    """安全地格式化URL组件"""
    if not text or not isinstance(text, str):
        return ""
    return urllib.parse.quote(text.strip(), safe='')

def is_valid_url(url_string):
    """验证URL格式"""
    if not url_string or not isinstance(url_string, str):
        return False
    try:
        result = urllib.parse.urlparse(url_string.strip())
        return all([result.scheme in ['http', 'https'], result.netloc])
    except (ValueError, AttributeError):
        return False

# 在辅助函数定义后验证配置URL
validate_config_urls()

# --- AI 模型调用函数 (SSE) ---
def call_ai_model_stream(prompt, history=None, user_api_key=None, user_api_endpoint=None, user_model_name=None, user_system_prompt=None, user_max_tokens=None):
    """调用 AI API 进行流式聊天。"""
    api_key = user_api_key
    api_endpoint = user_api_endpoint
    model_name = user_model_name
    system_prompt = user_system_prompt if user_system_prompt and user_system_prompt.strip() else config.DEFAULT_SYSTEM_PROMPT
    max_tokens = user_max_tokens if user_max_tokens is not None and user_max_tokens > 0 else None
    api_key_for_log = f"***{api_key[-4:]}" if api_key and len(api_key) >= 4 else ("Provided" if api_key else "None")

    if not api_key or not api_endpoint or not is_valid_url(api_endpoint):
        logging.warning(f"User API Key ({api_key_for_log}), Endpoint ('{api_endpoint}'), or validity issue. Using Pollinations fallback.")
        api_endpoint = config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT
        model_name = user_model_name or "openai"
        api_key = None
        system_prompt = user_system_prompt
        max_tokens = None
        logging.info(f"--- Using Pollinations Fallback (POST {api_endpoint}, Model: {model_name}) ---")
    else:
        logging.info(f"--- Using User API (Endpoint: {api_endpoint}, Model: {model_name}, Key Provided: {bool(api_key)}) ---")
        if system_prompt: logging.info(f"System Prompt Used: True (Length: {len(system_prompt)})")
        if max_tokens: logging.info(f"Max Tokens: {max_tokens}")

    messages = []
    if system_prompt and system_prompt.strip(): messages.append({"role": "system", "content": system_prompt.strip()})
    if history and isinstance(history, list):
        valid_history = [item for item in history if isinstance(item, dict) and 'role' in item and 'content' in item and item.get('content')]
        messages.extend(valid_history)
    if prompt and isinstance(prompt, str) and prompt.strip(): messages.append({"role": "user", "content": prompt.strip()})
    elif not messages:
        logging.error("No valid user prompt or history provided.")
        yield f"event: error\ndata: {json.dumps({'error': '内部错误：缺少有效的用户输入'})}\n\n"; return

    headers = {"Content-Type": "application/json", "Accept": "text/event-stream"}
    if api_key: headers["Authorization"] = f"Bearer {api_key}"

    data = {"messages": messages, "stream": True}
    if model_name: data["model"] = model_name
    if max_tokens: data["max_tokens"] = max_tokens

    log_data_brief = {k: v for k, v in data.items() if k != 'messages'}
    log_data_brief['num_messages'] = len(messages)
    logging.info(f"Sending POST request to {api_endpoint} (brief): {json.dumps(log_data_brief, ensure_ascii=False)}")
    if messages: logging.debug(f"Last message hint: Role={messages[-1]['role']}")

    response = None
    try:
        response = requests.post(api_endpoint, headers=headers, json=data, stream=True, timeout=180)
        logging.info(f"Received API response status: {response.status_code}")

        if not response.ok:
            error_body = ""; status_code = response.status_code
            try: error_body = response.text[:500]; logging.error(f"API Error Response Body (partial): {error_body}")
            except Exception as read_err: logging.error(f"Reading API error response body failed: {read_err}")
            try: error_json = response.json(); error_detail = error_json.get('error', {}).get('message') or error_json.get('error') or error_body
            except: error_detail = error_body
            if status_code == 401: final_error_msg = "API Key 无效或未授权 (401)。"
            elif status_code == 404: final_error_msg = f"Endpoint 或模型未找到 (404): {api_endpoint}。详情: {error_detail}"
            elif status_code == 429: final_error_msg = "请求过于频繁 (429)。检查 API 配额。"
            elif status_code == 400: final_error_msg = f"请求无效 (400)。检查参数。详情: {error_detail}"
            elif status_code >= 500: final_error_msg = f"AI 服务端错误 ({status_code})。详情: {error_detail}"
            else: final_error_msg = f"API 请求失败 ({status_code})。详情: {error_detail}"
            logging.error(f"API Request Failed: {final_error_msg} (Status: {status_code})")
            yield f"event: error\ndata: {json.dumps({'error': final_error_msg})}\n\n"; return

        logging.info("Processing API stream...")
        buffer = ""
        for line_bytes in response.iter_lines(decode_unicode=False):
            if not line_bytes: continue
            try: decoded_line = line_bytes.decode('utf-8', errors='replace').strip()
            except UnicodeDecodeError as ude: logging.warning(f"Unicode decode error: {ude}"); continue
            if not decoded_line: continue

            if decoded_line.startswith('data:'):
                content_json_str = decoded_line[len('data:'):].strip()
                if content_json_str == '[DONE]': logging.info("[DONE] received."); break
                if not content_json_str: continue
                buffer += content_json_str
                try:
                    chunk = json.loads(buffer); buffer = ""
                    choices = chunk.get("choices")
                    if choices and isinstance(choices, list) and len(choices) > 0:
                        choice = choices[0]
                        if isinstance(choice, dict):
                            delta = choice.get("delta", {})
                            if isinstance(delta, dict):
                                content_piece = delta.get("content")
                                if content_piece: yield f"event: message\ndata: {json.dumps({'text': content_piece}, ensure_ascii=False)}\n\n"
                            # else: logging.debug(f"Delta is not dict: {delta}")
                        # else: logging.debug(f"Choice is not dict: {choice}")
                    elif 'error' in chunk:
                        error_msg = chunk['error']; logging.error(f"Stream error: {error_msg}")
                        yield f"event: error\ndata: {json.dumps({'error': f'AI 服务错误: {error_msg}'})}\n\n"; return
                    # else: logging.debug(f"Chunk without choices/error: {content_json_str}")
                except json.JSONDecodeError:
                    if len(buffer) > 1024 * 10: logging.error("Buffer limit exceeded."); buffer = ""
                    # else: logging.debug("Incomplete JSON, buffering...")
                except Exception as e_inner:
                     buffer = ""; logging.error(f"Inner stream processing error: {e_inner}. Chunk (partial): {content_json_str[:200]}", exc_info=False)
                     yield f"event: error\ndata: {json.dumps({'error': '内部处理错误'})}\n\n"; return
            # else: logging.debug(f"Non-data line: {decoded_line}")

        logging.info("Finished stream loop.")
        yield "event: done\ndata: {}\n\n"

    except requests.exceptions.Timeout:
        logging.error("API request timed out.")
        yield f"event: error\ndata: {json.dumps({'error': '请求 AI 服务超时'})}\n\n"
    except requests.exceptions.ChunkedEncodingError as e:
        logging.error(f"Chunked Encoding Error: {e}", exc_info=False)
        yield f"event: error\ndata: {json.dumps({'error': '读取响应流时连接中断'})}\n\n"
    except requests.exceptions.RequestException as e:
        logging.error(f"Network/Connection error: {e}", exc_info=False)
        yield f"event: error\ndata: {json.dumps({'error': f'网络或连接错误 ({e.__class__.__name__})'})}\n\n"
    except Exception as e_outer:
        logging.error(f"Unknown stream error: {e_outer}", exc_info=True)
        yield f"event: error\ndata: {json.dumps({'error': '未知内部错误'})}\n\n"
    finally:
        if response is not None: response.close(); logging.debug("API response closed.")
        logging.info("Stream generator finished.")

# --- 智能体相关路由 ---
@app.route('/api/agents', methods=['POST'])
def create_agent():
    """创建新的智能体并存储"""
    try:
        data = request.get_json()
        validate_json_input(data, required_fields=['name', 'system_prompt'])

        # 验证输入参数
        agent_name = validate_string_input(
            data.get('name'),
            '智能体名称',
            max_length=100,
            min_length=1
        )

        system_prompt = validate_string_input(
            data.get('system_prompt'),
            '系统提示词',
            max_length=10000,
            min_length=1
        )

        # 验证模型配置（可选）
        model_config = data.get('model_config', {})
        if model_config and not isinstance(model_config, dict):
            return jsonify({"error": "模型配置必须是对象类型"}), 400

        agents = load_agents()

        # 检查名称是否重复
        if any(agent['name'] == agent_name for agent in agents):
            return jsonify({"error": f"名称为 '{agent_name}' 的智能体已存在"}), 409

        new_agent = {
            "id": str(os.urandom(8).hex()),
            "name": agent_name,
            "system_prompt": system_prompt,
            "model_config": model_config,
            "created_at": datetime.now(datetime.timezone.utc).isoformat()
        }
        agents.append(new_agent)
        save_agents(agents)

        logging.info(f"Created new agent: {agent_name} (ID: {new_agent['id']})")
        return jsonify(new_agent), 201

    except ValueError as e:
        logging.warning(f"Agent creation validation error: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error(f"Error creating agent: {e}", exc_info=True)
        return jsonify({"error": "创建智能体时发生内部错误"}), 500

@app.route('/api/agents', methods=['GET'])
def list_agents():
    """列出所有已创建的智能体"""
    agents = load_agents()
    return jsonify(agents)

@app.route('/api/agents/<agent_id>', methods=['GET'])
def get_agent(agent_id):
    """获取特定智能体的详细信息"""
    agents = load_agents()
    agent = next((agent for agent in agents if agent['id'] == agent_id), None)
    if agent:
        return jsonify(agent)
    else:
        return jsonify({"error": "智能体未找到"}), 404

@app.route('/api/agents/<agent_id>', methods=['PUT'])
def update_agent(agent_id):
    """更新特定智能体的信息"""
    try:
        # 验证agent_id
        agent_id = validate_string_input(agent_id, 'agent_id', max_length=50)

        data = request.get_json()
        validate_json_input(data)

        agents = load_agents()
        agent_index = next((index for index, agent in enumerate(agents) if agent['id'] == agent_id), -1)

        if agent_index == -1:
            return jsonify({"error": "智能体未找到"}), 404

        # 验证并更新字段
        if 'name' in data:
            new_name = validate_string_input(
                data['name'],
                '智能体名称',
                max_length=100,
                min_length=1
            )
            # 检查新名称是否与现有其他智能体冲突 (排除自身)
            if any(agent['name'] == new_name and agent['id'] != agent_id for agent in agents):
                return jsonify({"error": f"名称为 '{new_name}' 的智能体已存在"}), 409
            agents[agent_index]['name'] = new_name

        if 'system_prompt' in data:
            system_prompt = validate_string_input(
                data['system_prompt'],
                '系统提示词',
                max_length=10000,
                min_length=1
            )
            agents[agent_index]['system_prompt'] = system_prompt

        if 'model_config' in data:
            model_config = data['model_config']
            if model_config and not isinstance(model_config, dict):
                return jsonify({"error": "模型配置必须是对象类型"}), 400
            agents[agent_index]['model_config'] = model_config

        agents[agent_index]['updated_at'] = datetime.now(datetime.timezone.utc).isoformat()

        save_agents(agents)
        logging.info(f"Updated agent: {agents[agent_index]['name']} (ID: {agent_id})")
        return jsonify(agents[agent_index])

    except ValueError as e:
        logging.warning(f"Agent update validation error: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error(f"Error updating agent {agent_id}: {e}", exc_info=True)
        return jsonify({"error": "更新智能体时发生内部错误"}), 500

@app.route('/api/agents/<agent_id>', methods=['DELETE'])
def delete_agent(agent_id):
    """删除特定智能体"""
    agents = load_agents()
    initial_length = len(agents)
    agents = [agent for agent in agents if agent['id'] != agent_id]

    if len(agents) == initial_length:
        return jsonify({"error": "智能体未找到"}), 404

    save_agents(agents)
    logging.info(f"Deleted agent (ID: {agent_id})")
    return jsonify({"message": "智能体已删除"}), 200

# --- 路由定义 ---

@app.route('/api/logs/app', methods=['GET'])
def get_app_logs():
    """提供应用日志文件的最后 N 行。"""
    lines_to_fetch = request.args.get('lines', default=200, type=int)
    if lines_to_fetch <= 0 or lines_to_fetch > 2000: # 限制行数防止过大请求
        lines_to_fetch = 200
    try:
        # 使用配置的日志文件路径
        current_log_file = os.path.join(log_dir, 'app.log')
        if not os.path.exists(current_log_file):
            logging.error(f"Log file not found at: {current_log_file}")
            return jsonify({"error": "Log file not found."}), 404

        log_lines = []
        with open(current_log_file, 'r', encoding='utf-8') as f:
            # 读取所有行，然后取最后 N 行，更高效处理大文件
            all_lines = f.readlines()
            log_lines = all_lines[-lines_to_fetch:]
        
        # 移除可能的换行符，确保JSON格式正确
        log_lines = [line.strip() for line in log_lines]

        return jsonify({"log_lines": log_lines})
    except Exception as e:
        logging.error(f"Error reading log file: {e}", exc_info=True)
        return jsonify({"error": "Failed to read log file."}), 500


@app.route('/', methods=['GET'])
def index():
    """提供主页面"""
    logging.info("Serving index page")
    default_model_name = "openai"
    return render_template('Index.html', backend_default_model=default_model_name)

@app.route('/static/<path:subpath>')
def serve_static(subpath):
    """提供静态文件"""
    return send_from_directory(app.static_folder, subpath)

@app.route('/api/templates', methods=['GET'])
def get_api_templates():
    """获取 API 模板"""
    templates_file_path = os.path.join(os.path.dirname(__file__), config.API_TEMPLATES_FILE)
    logging.info(f"Loading API templates from: {templates_file_path}")
    try:
        if not os.path.exists(templates_file_path):
            logging.error(f"API templates file not found: {templates_file_path}")
            return jsonify({"error": "API 模板文件未找到"}), 404
        with open(templates_file_path, 'r', encoding='utf-8') as f: return jsonify(json.load(f))
    except Exception as e: logging.error(f"Error reading API templates: {e}", exc_info=True); return jsonify({"error": "读取 API 模板错误"}), 500

@app.route('/api/models/text', methods=['GET'])
def get_text_model_list():
    """获取文本模型列表 - 优先本地配置，后台更新网络数据"""
    try:
        # 检查是否强制更新
        force_update = request.args.get('force_update', 'false').lower() == 'true'
        models = get_text_models(force_update=force_update)

        logging.info(f"Returning {len(models)} text models (force_update={force_update})")
        return jsonify(models)
    except Exception as e:
        logging.error(f"Error getting text models: {e}", exc_info=True)
        return jsonify({"error": "获取文本模型失败"}), 500

@app.route('/api/models/image', methods=['GET'])
def get_image_model_list():
    """获取图像模型列表 - 优先本地配置，后台更新网络数据"""
    try:
        # 检查是否强制更新
        force_update = request.args.get('force_update', 'false').lower() == 'true'
        models = get_image_models(force_update=force_update)

        logging.info(f"Returning {len(models)} image models (force_update={force_update})")
        return jsonify(models)
    except Exception as e:
        logging.error(f"Error getting image models: {e}", exc_info=True)
        return jsonify({"error": "获取图像模型失败"}), 500

@app.route('/api/models/audio_voices', methods=['GET'])
def get_audio_voice_list():
    """获取音频语音列表 - 优先本地配置，后台更新网络数据"""
    try:
        # 检查是否强制更新
        force_update = request.args.get('force_update', 'false').lower() == 'true'
        voices = get_audio_voices(force_update=force_update)

        logging.info(f"Returning {len(voices)} audio voices (force_update={force_update})")
        return jsonify(voices)

    except Exception as e:
        logging.error(f"Error getting audio voices: {e}", exc_info=True)
        return jsonify({"error": "获取语音列表失败"}), 500

# 新增模型管理端点
@app.route('/api/models/force_update', methods=['POST'])
def force_update_models_endpoint():
    """强制从网络更新所有模型列表"""
    try:
        result = force_update_models()
        return jsonify({
            "success": result['success'],
            "updated": result,
            "message": "模型列表更新完成" if result['success'] else "模型列表更新失败"
        })
    except Exception as e:
        logging.error(f"Error in force update models: {e}", exc_info=True)
        return jsonify({"error": "强制更新模型失败"}), 500

@app.route('/api/models/status', methods=['GET'])
def get_models_status_endpoint():
    """获取模型列表状态信息"""
    try:
        status = get_models_status()
        return jsonify(status)
    except Exception as e:
        logging.error(f"Error getting models status: {e}", exc_info=True)
        return jsonify({"error": "获取模型状态失败"}), 500

@app.route('/api/models/clear_cache', methods=['POST'])
def clear_models_cache_endpoint():
    """清除模型缓存"""
    try:
        clear_models_cache()
        return jsonify({"success": True, "message": "模型缓存已清除"})
    except Exception as e:
        logging.error(f"Error clearing models cache: {e}", exc_info=True)
        return jsonify({"error": "清除模型缓存失败"}), 500

@app.route('/api/system_prompt_templates', methods=['GET'])
def get_system_prompt_templates():
    """获取系统提示词内置模板"""
    templates_file_path = os.path.join(os.path.dirname(__file__), 'system_prompt_templates.json')
    logging.info(f"Loading system prompt templates from: {templates_file_path}")
    try:
        if not os.path.exists(templates_file_path):
            logging.error(f"System prompt templates file not found: {templates_file_path}")
            return jsonify({"error": "系统提示词模板文件未找到"}), 404
        with open(templates_file_path, 'r', encoding='utf-8') as f:
            templates = json.load(f)
            return jsonify(templates)
    except Exception as e:
        logging.error(f"Error reading system prompt templates: {e}", exc_info=True)
        return jsonify({"error": "读取系统提示词模板错误"}), 500

@app.route('/api/logs', methods=['GET'], endpoint='get_all_logs')
@rate_limit_check('logs', limit=10, window=60)  # 限制日志查看频率
def get_all_logs():
    """获取应用运行日志 (最新的 N 行)"""
    try:
        # 验证和清理输入参数
        lines_param = request.args.get('lines', default='100')
        try:
            lines_to_fetch = int(lines_param)
            if lines_to_fetch <= 0 or lines_to_fetch > 2000:  # 限制最大行数
                lines_to_fetch = 100
        except (ValueError, TypeError):
            lines_to_fetch = 100  # 默认值
        current_log_file = os.path.join(log_dir, 'app.log')
        if not os.path.exists(current_log_file):
            return jsonify({"error": "日志文件未找到"}), 404

        log_lines = []
        with open(current_log_file, 'r', encoding='utf-8') as f:
            # 读取所有行，然后取最后 N 行，更简单但可能对大文件有性能影响
            # 对于非常大的日志文件，可以考虑更高效的读取方式，如反向读取
            all_lines = f.readlines()
            log_lines = all_lines[-lines_to_fetch:]
        
        # 对日志中的 api_key 进行二次脱敏检查 (虽然主要脱敏已在记录时完成)
        sanitized_log_lines = []
        for line in log_lines:
            # 这是一个简单的示例，实际场景可能需要更复杂的正则匹配来确保所有 api_key 都被覆盖
            # 假设 api_key 的格式是 'sk-...' 或其他已知前缀
            # 注意：这里的脱敏是针对日志查看接口的，日志文件本身可能仍包含敏感信息（除非写入时已完全脱敏）
            # 目前的日志记录逻辑中 api_key_for_log 已经做了脱敏，这里主要是防御性检查
            # 如果需要更严格的脱敏，应在日志写入时就处理
            # 为简化，此处仅返回原始日志行，依赖写入时的脱敏
            sanitized_log_lines.append(line.strip()) # strip() 去除换行符
            
        return jsonify({"logs": sanitized_log_lines})
    except Exception as e:
        logging.error(f"Error fetching app logs: {e}", exc_info=True)
        return jsonify({"error": "获取应用日志失败"}), 500


@app.route('/api/log_client_error', methods=['POST'])
def log_client_error():
    """记录客户端错误到服务器日志"""
    try:
        error_data = request.get_json()
        if not error_data:
            return jsonify({"error": "No error data provided"}), 400

        # 构建错误信息
        error_type = error_data.get('type', 'javascript_error')
        message = error_data.get('message', 'Unknown error')
        filename = error_data.get('filename', 'unknown')
        lineno = error_data.get('lineno', 'unknown')
        colno = error_data.get('colno', 'unknown')
        stack = error_data.get('stack', 'No stack trace')
        timestamp = error_data.get('timestamp', 'unknown')
        user_agent = error_data.get('userAgent', 'unknown')

        # 记录到日志
        if error_type == 'unhandledrejection':
            reason = error_data.get('reason', 'Unknown reason')
            logging.error(f"CLIENT PROMISE REJECTION - Reason: {reason}, Timestamp: {timestamp}, UserAgent: {user_agent}")
        else:
            logging.error(f"CLIENT JS ERROR - Message: {message}, File: {filename}:{lineno}:{colno}, Stack: {stack}, Timestamp: {timestamp}, UserAgent: {user_agent}")

        return jsonify({"status": "logged"}), 200
    except Exception as e:
        logging.error(f"Error logging client error: {e}", exc_info=True)
        return jsonify({"error": "Failed to log client error"}), 500

@app.route('/api/log_error', methods=['POST'])
def log_error():
    data = request.json
    # 这里可以将错误写入日志文件、数据库等
    print("前端错误日志：", data)
    return {'status': 'ok'}

AGENTS_FILE = os.path.join(os.path.dirname(__file__), 'agents.json')
agents_lock = threading.RLock()  # 用于保护agents文件操作

def load_agents():
    """线程安全的agents加载"""
    with agents_lock:
        if not os.path.exists(AGENTS_FILE):
            return []
        try:
            with open(AGENTS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"Error loading agents file: {e}", exc_info=True)
            return []

def save_agents(agents):
    """线程安全的agents保存"""
    with agents_lock:
        try:
            # 先写入临时文件，然后原子性替换
            temp_file = AGENTS_FILE + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(agents, f, ensure_ascii=False, indent=4)

            # 原子性替换
            if os.name == 'nt':  # Windows
                if os.path.exists(AGENTS_FILE):
                    os.remove(AGENTS_FILE)
                os.rename(temp_file, AGENTS_FILE)
            else:  # Unix/Linux
                os.rename(temp_file, AGENTS_FILE)

        except Exception as e:
            logging.error(f"Error saving agents file: {e}", exc_info=True)
            # 清理临时文件
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except:
                pass

@app.route('/chat', methods=['GET', 'POST'])
@rate_limit_check('chat', limit=30, window=60)  # 每分钟最多30次聊天请求
def chat():
    """处理聊天请求（智能HTTP方法选择）"""
    logging.info(f"--- /chat {request.method} request received ---")
    try:
        # 智能获取请求数据
        if request.method == 'GET':
            # GET请求：从查询参数获取数据（适用于短文本、隐性消息）
            encoded_data = request.args.get('data')
            if not encoded_data:
                logging.warning("Missing 'data' query parameter")
                def error_stream(): yield f"event: error\ndata: {json.dumps({'error': '请求缺少 data 参数'})}\n\n"
                return Response(error_stream(), content_type='text/event-stream; charset=utf-8', status=400)
            try:
                data = json.loads(encoded_data)
            except Exception as json_err:
                logging.error(f"Failed to parse 'data': {json_err}", exc_info=False)
                def error_stream(): yield f"event: error\ndata: {json.dumps({'error': '无法解析请求数据'})}\n\n"
                return Response(error_stream(), content_type='text/event-stream; charset=utf-8', status=400)
        else:
            # POST请求：从请求体获取JSON数据（适用于长文本、显性消息）
            data = request.get_json()
            if not data:
                logging.warning("Missing JSON data in POST request")
                def error_stream(): yield f"event: error\ndata: {json.dumps({'error': '请求缺少 JSON 数据'})}\n\n"
                return Response(error_stream(), content_type='text/event-stream; charset=utf-8', status=400)

        user_prompt = data.get('prompt')
        actual_history = data.get('history', [])
        user_system_prompt = data.get('system_prompt')
        user_max_tokens = data.get('max_tokens')


        logging.info(f"Prompt Length: {len(user_prompt) if user_prompt else 0}, History Length: {len(actual_history)}")
        if user_system_prompt: logging.info(f"System Prompt Provided: True")
        if user_max_tokens: logging.info(f"Max Tokens: {user_max_tokens}")


        user_api_key = data.get('api_key')
        user_api_endpoint = data.get('api_endpoint')
        user_model_name = data.get('model_name')
        logging.info(f"Target Model: {user_model_name or 'Default'}, API Key Provided: {bool(user_api_key)}")

        if not user_prompt:
            logging.error("Empty Prompt.")
            def error_stream(): yield f"event: error\ndata: {json.dumps({'error': '输入不能为空'})}\n\n"
            return Response(error_stream(), content_type='text/event-stream; charset=utf-8', status=400)

        if not isinstance(actual_history, list): actual_history = []
        else: actual_history = [item for item in actual_history if isinstance(item, dict) and 'role' in item and 'content' in item]


        stream = call_ai_model_stream(
            prompt=user_prompt.strip() if user_prompt else "", history=actual_history,
            user_api_key=user_api_key, user_api_endpoint=user_api_endpoint, user_model_name=user_model_name,
            user_system_prompt=user_system_prompt, user_max_tokens=user_max_tokens
        )
        return Response(stream_with_context(stream), content_type='text/event-stream; charset=utf-8')

    except Exception as e:
         logging.error(f"Error processing /chat: {e}", exc_info=True)
         def error_stream(): yield f"event: error\ndata: {json.dumps({'error': '意外的服务器错误'})}\n\n"
         return Response(error_stream(), content_type='text/event-stream; charset=utf-8', status=500)

# --- Image Generation Endpoint ---
@app.route('/api/generate_image_url', methods=['POST'])
def generate_image_url():
    """生成 Pollinations 图片 URL"""
    try:
        data = request.get_json()
        validate_json_input(data, required_fields=['prompt'])

        # 验证输入参数
        prompt = validate_string_input(
            data.get('prompt'),
            'prompt',
            max_length=2000,
            min_length=1
        )

        img_model = validate_string_input(
            data.get('model', 'flux'),
            'model',
            max_length=50,
            required=False
        ) or 'flux'

        width = validate_integer_input(
            data.get('width', 1024),
            'width',
            min_value=64,
            max_value=2048,
            required=False
        ) or 1024

        height = validate_integer_input(
            data.get('height', 1024),
            'height',
            min_value=64,
            max_value=2048,
            required=False
        ) or 1024

        # 验证可选参数
        seed = data.get('seed')
        if seed is not None:
            seed = validate_integer_input(seed, 'seed', min_value=0, required=False)

        nologo = bool(data.get('nologo', True))
        enhance = bool(data.get('enhance', False))
        safe = bool(data.get('safe', True))

        # 新增参数：负面提示词和referrer
        negative_prompt = validate_string_input(
            data.get('negative_prompt', ''),
            'negative_prompt',
            max_length=1000,
            required=False
        ) or ''

        referrer = validate_string_input(
            data.get('referrer', 'https://pollinations.ai'),
            'referrer',
            max_length=200,
            required=False
        ) or 'https://pollinations.ai'

        formatted_prompt = format_pollinations_url_component(prompt)
        params = {
            "model": img_model,
            "width": width,
            "height": height,
            "nologo": str(nologo).lower(),
            "enhance": str(enhance).lower(),
            "safe": str(safe).lower(),
            "referrer": referrer
        }

        if seed is not None:
            params["seed"] = seed

        # 如果有负面提示词，添加到参数中
        if negative_prompt:
            params["negative"] = format_pollinations_url_component(negative_prompt)

        query_string = urllib.parse.urlencode(params)
        image_url = f"{config.POLLINATIONS_IMAGE_ENDPOINT}{formatted_prompt}?{query_string}"

        logging.info(f"Generated Image URL (Model: {img_model}, Size: {width}x{height})")
        return jsonify({"imageUrl": image_url})

    except ValueError as e:
        logging.warning(f"Image generation validation error: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error(f"Error generating image URL: {e}", exc_info=True)
        return jsonify({"error": "生成图片URL时发生内部错误"}), 500

@app.route('/api/optimize_prompt', methods=['POST'])
@rate_limit_check('optimize_prompt', limit=15, window=60)
def optimize_prompt():
    """根据用户反馈优化提示词"""
    try:
        data = request.get_json()
        validate_json_input(data, required_fields=['userInput'])

        user_input = validate_string_input(
            data.get('userInput'),
            'userInput',
            max_length=1000,
            min_length=1
        )

        current_prompt = validate_string_input(
            data.get('currentPrompt', ''),
            'currentPrompt',
            max_length=2000,
            required=False
        ) or ''

        # 构建提示词优化的系统提示
        system_prompt = """你是一个专业的AI图像生成提示词优化专家。根据用户的当前提示词和新的需求描述，生成一个优化后的完整提示词。

当前提示词：
{current_prompt}

用户的新需求：
{user_input}

请根据以下原则优化提示词：

1. **保留核心内容**：保持原提示词的主要元素和意图
2. **融合新需求**：将用户的新需求自然地融入到提示词中
3. **增强细节**：添加更多具体的视觉细节、风格描述、光线效果等
4. **优化结构**：使用更专业的表达方式和更好的组织结构
5. **英文输出**：生成英文提示词，更适合图像生成模型

请直接输出优化后的完整英文提示词，不要包含解释或其他内容。如果当前提示词为空，则根据用户需求创建全新的提示词。""".format(
            current_prompt=current_prompt if current_prompt else "（无）",
            user_input=user_input
        )

        # 调用AI进行提示词优化
        response = None
        try:
            endpoint = config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT
            headers = {"Content-Type": "application/json"}
            payload = {
                "model": "openai",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"请优化提示词。当前：{current_prompt}，新需求：{user_input}"}
                ],
                "max_tokens": 300,
                "temperature": 0.7
            }

            response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
            logging.info(f"Prompt optimization API status: {response.status_code}")
            response.raise_for_status()

            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                optimized_prompt = result['choices'][0]['message']['content'].strip()

                # 清理可能的引号或多余字符
                optimized_prompt = optimized_prompt.strip('"\'')

                logging.info(f"Prompt optimized from: {current_prompt[:50]}... to: {optimized_prompt[:50]}...")
                return jsonify({"optimizedPrompt": optimized_prompt})
            else:
                raise ValueError("AI未返回有效的优化提示词")

        except requests.exceptions.RequestException as e:
            logging.error(f"Prompt optimization request failed: {e}")
            return jsonify({"error": "网络请求失败，请稍后重试"}), 500
        except Exception as e:
            logging.error(f"Prompt optimization processing failed: {e}")
            return jsonify({"error": "处理优化时发生错误"}), 500
        finally:
            if response:
                try:
                    response.close()
                except Exception as cleanup_error:
                    logging.warning(f"Error closing prompt optimization response: {cleanup_error}")

    except ValueError as e:
        logging.warning(f"Prompt optimization validation error: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error(f"Error in prompt optimization: {e}", exc_info=True)
        return jsonify({"error": "提示词优化时发生内部错误"}), 500

@app.route('/api/auto_select_model', methods=['POST'])
@rate_limit_check('auto_select_model', limit=20, window=60)
def auto_select_model():
    """AI自动根据提示词选择最合适的图像生成模型"""
    try:
        data = request.get_json()
        validate_json_input(data, required_fields=['prompt'])

        prompt = validate_string_input(
            data.get('prompt'),
            'prompt',
            max_length=2000,
            min_length=1
        )

        # 构建模型选择的系统提示
        system_prompt = """你是一个专业的AI图像生成模型选择专家。根据用户的提示词，从以下可用模型中选择最合适的一个：

可用模型：
- flux: 通用模型，适合大多数场景
- flux-pro: 高质量模型，适合需要精细细节的图像
- flux-realism: 写实风格，适合真实感强的人像、风景、物体
- flux-anime: 动漫风格，适合卡通、插画、二次元角色
- flux-3d: 3D渲染风格，适合立体感强的场景和物体
- flux-cablyai: 艺术风格，适合创意设计、抽象艺术
- flux-schnell: 快速生成，适合简单图像或快速预览
- turbo: 高速模型，适合批量生成或简单场景

请分析提示词的内容、风格需求、质量要求等因素，选择最合适的模型。

只需要回复模型ID（如：flux-realism），不要解释。"""

        # 调用AI进行模型选择
        response = None
        try:
            endpoint = config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT
            headers = {"Content-Type": "application/json"}
            payload = {
                "model": "openai",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"提示词：{prompt}"}
                ],
                "max_tokens": 50,
                "temperature": 0.3
            }

            response = requests.post(endpoint, headers=headers, json=payload, timeout=30)
            logging.info(f"Auto model selection API status: {response.status_code}")
            response.raise_for_status()

            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                selected_model = result['choices'][0]['message']['content'].strip().lower()

                # 验证选择的模型是否在可用列表中
                available_models = ['flux', 'flux-pro', 'flux-realism', 'flux-anime',
                                  'flux-3d', 'flux-cablyai', 'flux-schnell', 'turbo']

                if selected_model in available_models:
                    logging.info(f"AI selected model: {selected_model} for prompt: {prompt[:50]}...")
                    return jsonify({"selectedModel": selected_model})
                else:
                    # 如果AI返回的模型不在列表中，使用默认模型
                    logging.warning(f"AI returned invalid model: {selected_model}, using default")
                    return jsonify({"selectedModel": "flux"})
            else:
                raise ValueError("AI未返回有效的模型选择")

        except requests.exceptions.RequestException as e:
            logging.error(f"Auto model selection request failed: {e}")
            # 网络错误时使用默认模型
            return jsonify({"selectedModel": "flux"})
        except Exception as e:
            logging.error(f"Auto model selection processing failed: {e}")
            # 处理错误时使用默认模型
            return jsonify({"selectedModel": "flux"})
        finally:
            if response:
                try:
                    response.close()
                except Exception as cleanup_error:
                    logging.warning(f"Error closing auto model selection response: {cleanup_error}")

    except ValueError as e:
        logging.warning(f"Auto model selection validation error: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error(f"Error in auto model selection: {e}", exc_info=True)
        return jsonify({"error": "自动模型选择时发生内部错误"}), 500

@app.route('/api/enhance_prompt', methods=['POST'])
@rate_limit_check('enhance_prompt', limit=10, window=60)
def enhance_prompt():
    """使用AI优化图像生成提示词"""
    try:
        data = request.get_json()
        validate_json_input(data, required_fields=['prompt'])

        original_prompt = validate_string_input(
            data.get('prompt'),
            'prompt',
            max_length=2000,
            min_length=1
        )

        # 构建优化提示词的系统提示
        system_prompt = """你是一个专业的AI图像生成提示词优化专家。请将用户提供的简单描述优化为更详细、更具艺术性的英文提示词。

优化要求：
1. 保持原意不变
2. 添加艺术风格、光影效果、构图等细节
3. 使用专业的艺术术语
4. 确保提示词适合AI图像生成
5. 输出为英文
6. 长度控制在200字以内

请直接输出优化后的提示词，不要添加额外说明。"""

        # 调用AI API优化提示词
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请优化这个图像描述：{original_prompt}"}
        ]

        headers = {"Content-Type": "application/json"}
        api_data = {
            "messages": messages,
            "model": "openai",  # 使用默认模型
            "max_tokens": 200,
            "temperature": 0.7
        }

        response = requests.post(
            config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT,
            headers=headers,
            json=api_data,
            timeout=30
        )

        if response.ok:
            response_data = response.json()
            if 'choices' in response_data and len(response_data['choices']) > 0:
                enhanced_prompt = response_data['choices'][0]['message']['content'].strip()
                logging.info(f"Prompt enhanced successfully")
                return jsonify({"enhancedPrompt": enhanced_prompt}), 200
            else:
                raise Exception("AI API返回格式异常")
        else:
            raise Exception(f"AI API请求失败: {response.status_code}")

    except ValueError as e:
        logging.warning(f"Prompt enhancement validation error: {e}")
        return jsonify({"error": str(e)}), 400
    except Exception as e:
        logging.error(f"Error enhancing prompt: {e}", exc_info=True)
        return jsonify({"error": "优化提示词时发生错误"}), 500

# --- TTS功能已迁移到前端直接调用Pollinations AI GET API ---
# 根据Pollinations AI官方文档，TTS使用GET请求：
# GET https://text.pollinations.ai/{prompt}?model=openai-audio&voice={voice}
# 这符合第三原则：隐性消息（如语音朗读）使用GET方法



# --- Image Prompt Enhancement Endpoint ---
@app.route('/api/enhance_prompt', methods=['POST'])
def enhance_image_prompt():
    """优化图像提示词"""
    data = request.get_json(); original_prompt = data.get('prompt', '').strip() if data else ''
    if not original_prompt: return jsonify({"error": "prompt 不能为空"}), 400
    logging.info(f"Received enhance prompt request (Length: {len(original_prompt)})")
    enhancer_model = "openai"
    system_prompt_enhancer = "You are an expert image prompt enhancer. Take the user's prompt and expand on it, adding vivid details, style suggestions (like 'photorealistic', 'cinematic lighting', 'anime style'), and composition ideas to make it much more descriptive for an AI image generator. Respond only with the enhanced prompt, no preamble or explanations."

    response = None
    try:
        enhancer_endpoint = config.POLLINATIONS_OPENAI_COMPATIBLE_ENDPOINT
        headers = {"Content-Type": "application/json"}
        payload = { "model": enhancer_model, "messages": [ {"role": "system", "content": system_prompt_enhancer}, {"role": "user", "content": original_prompt} ], "max_tokens": 250, "temperature": 0.7 }
        response = requests.post(enhancer_endpoint, headers=headers, json=payload, timeout=30)
        logging.info(f"Prompt enhancement API status: {response.status_code}")
        response.raise_for_status()
        result = response.json()

        enhanced_prompt = None; choices = result.get('choices')
        if choices and isinstance(choices, list) and len(choices) > 0:
             message = choices[0].get('message')
             if message and isinstance(message, dict): enhanced_prompt = message.get('content')

        if enhanced_prompt:
            logging.info(f"Enhanced prompt received (Length: {len(enhanced_prompt)})")
            return jsonify({"enhancedPrompt": enhanced_prompt.strip()})
        else:
            logging.error(f"Prompt enhance failed: No content. Raw: {result}")
            return jsonify({"error": "优化提示词失败：AI 未返回有效内容"}), 500

    except requests.exceptions.Timeout:
        logging.error("Prompt enhance timed out.")
        return jsonify({"error": "优化提示词请求超时"}), 504
    except requests.exceptions.HTTPError as e:
        logging.error(f"Prompt enhance HTTP error: {e.response.status_code} - {e.response.text[:200]}")
        return jsonify({"error": f"优化服务返回错误 ({e.response.status_code})"}), e.response.status_code
    except requests.exceptions.RequestException as e:
        logging.error(f"Prompt enhance request failed: {e}")
        return jsonify({"error": f"请求优化服务失败: {e.__class__.__name__}"}), 502
    except Exception as e:
        logging.error(f"Unknown prompt enhance error: {e}", exc_info=True)
        return jsonify({"error": "处理提示词优化时发生未知错误"}), 500
    finally:
        # 确保响应资源被正确清理
        if response is not None:
            try:
                response.close()
                logging.debug("Prompt enhance response closed.")
            except Exception as cleanup_error:
                logging.warning(f"Error closing prompt enhance response: {cleanup_error}")

# --- 文件上传功能 ---
@app.route('/api/upload_file', methods=['POST'])
@rate_limit_check('upload_file', limit=10, window=60)  # 每分钟最多10次文件上传
def upload_file():
    """处理文件上传"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({"error": "没有选择文件"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "文件名为空"}), 400

        # 文件类型验证
        allowed_extensions = {
            'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
            'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf', '.odt'],
            'spreadsheet': ['.xls', '.xlsx', '.csv', '.ods'],
            'audio': ['.mp3', '.wav', '.ogg', '.m4a', '.flac'],
            'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
            'archive': ['.zip', '.rar', '.7z', '.tar', '.gz'],
            'code': ['.py', '.js', '.html', '.css', '.json', '.xml', '.yaml', '.yml']
        }

        file_ext = os.path.splitext(file.filename)[1].lower()
        file_type = None

        for category, extensions in allowed_extensions.items():
            if file_ext in extensions:
                file_type = category
                break

        if not file_type:
            return jsonify({"error": f"不支持的文件类型: {file_ext}"}), 400

        # 文件大小验证 (最大50MB)
        max_size = 50 * 1024 * 1024  # 50MB
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        if file_size > max_size:
            return jsonify({"error": "文件大小超过50MB限制"}), 400

        # 生成安全的文件名
        import uuid
        from werkzeug.utils import secure_filename

        secure_name = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4().hex}_{secure_name}"

        # 确保上传目录存在
        upload_dir = os.path.join('uploads', file_type)
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 记录上传信息
        logging.info(f"File uploaded successfully: {file.filename} -> {file_path} ({file_size} bytes)")

        # 返回成功响应
        return jsonify({
            "success": True,
            "message": "文件上传成功",
            "file_info": {
                "original_name": file.filename,
                "saved_name": unique_filename,
                "file_type": file_type,
                "file_size": file_size,
                "file_path": file_path,
                "upload_time": datetime.now().isoformat()
            }
        })

    except Exception as e:
        logging.error(f"File upload error: {e}", exc_info=True)
        return jsonify({"error": "文件上传失败"}), 500

@app.route('/api/uploaded_files', methods=['GET'])
def list_uploaded_files():
    """获取已上传文件列表"""
    try:
        files_info = []
        uploads_dir = 'uploads'

        if os.path.exists(uploads_dir):
            for category in os.listdir(uploads_dir):
                category_path = os.path.join(uploads_dir, category)
                if os.path.isdir(category_path):
                    for filename in os.listdir(category_path):
                        file_path = os.path.join(category_path, filename)
                        if os.path.isfile(file_path):
                            stat = os.stat(file_path)
                            files_info.append({
                                "filename": filename,
                                "category": category,
                                "size": stat.st_size,
                                "upload_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                                "file_path": file_path
                            })

        return jsonify({"files": files_info})

    except Exception as e:
        logging.error(f"Error listing uploaded files: {e}", exc_info=True)
        return jsonify({"error": "获取文件列表失败"}), 500

# --- 启动应用 ---
if __name__ == '__main__':
    logging.info("Preparing to run Flask application...")
    required_paths = [config.API_TEMPLATES_FILE, 'models_config.py', 'Index.html', app.static_folder]
    for path_item in required_paths:
        full_path = os.path.join(os.path.dirname(__file__), path_item)
        if not os.path.exists(full_path): logging.warning(f"!!! Required path not found: {full_path} !!!")
        else: logging.info(f"Required path found: {full_path}")

    print(f"Flask 应用准备就绪，请在浏览器访问 http://127.0.0.1:{config.PORT} 或 http://<你的IP>:{config.PORT}")
    app.run(host=config.HOST, port=config.PORT, debug=config.DEBUG_MODE, threaded=True, use_reloader=config.DEBUG_MODE)