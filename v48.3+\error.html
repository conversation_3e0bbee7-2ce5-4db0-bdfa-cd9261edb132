<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误</title>
    <style>
        body { font-family: sans-serif; margin: 20px; padding: 0; background-color: #f4f6f8; color: #333; display: flex; justify-content: center; align-items: center; min-height: 80vh; text-align: center; }
        .error-container { background-color: #fff; padding: 30px 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 500px; }
        h1 { color: #dc3545; margin-top: 0; }
        p { margin-bottom: 20px; line-height: 1.6; }
        a { color: #007bff; text-decoration: none; }
        a:hover { text-decoration: underline; }
        pre { background-color: #e9ecef; padding: 15px; border-radius: 4px; text-align: left; white-space: pre-wrap; word-break: break-all; color: #495057; max-height: 200px; overflow-y: auto; font-size: 0.9em; }
        /* Optional Dark Theme Adaptation (basic) */
        @media (prefers-color-scheme: dark) {
            body { background-color: #1e1e1e; color: #d4d4d4; }
            .error-container { background-color: #2a2a2a; box-shadow: 0 2px 10px rgba(0,0,0,0.3); }
            h1 { color: #f87171; }
            a { color: #60a5fa; }
            pre { background-color: #3a3a3a; color: #ccc; }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1>发生错误</h1>
        <p>{{ error_message | safe }}</p>
        <p><a href="/">返回首页</a></p>
        {% if error_details %}
           <p><strong>详细信息 (供调试):</strong></p>
           <pre>{{ error_details }}</pre>
        {% endif %}
    </div>
</body>
</html>
