<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的前端架构示例</title>
    
    <!-- 新的优化CSS架构 -->
    <link rel="stylesheet" href="static/css/variables.css">
    <link rel="stylesheet" href="static/css/utilities.css">
    <link rel="stylesheet" href="static/css/components.css">
    <link rel="stylesheet" href="static/css/themes-optimized.css">
    
    <!-- 保留的现有CSS文件（待迁移） -->
    <link rel="stylesheet" href="static/css/design-system.css">
    <link rel="stylesheet" href="static/css/dark_theme.css">
    
    <style>
        /* 示例：如何使用新的CSS变量系统 */
        .demo-section {
            padding: var(--spacing-lg);
            margin: var(--spacing-md) 0;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            background: var(--bg-secondary);
        }
        
        .demo-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }
        
        /* 示例：避免!important，使用更具体的选择器 */
        .demo-section .btn.btn--custom {
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
            border-color: var(--color-primary);
            box-shadow: var(--shadow-sm);
        }
    </style>
</head>
<body>
    <!-- 示例：使用工具类替代内联样式 -->
    <div class="u-p-lg u-bg-primary">
        <header class="u-flex u-items-center u-justify-between u-mb-lg">
            <h1 class="u-text-2xl u-font-bold u-text-primary u-m-0">
                前端架构优化示例
            </h1>
            <button id="theme-toggle" class="btn btn--secondary">
                切换主题
            </button>
        </header>

        <!-- 按钮组件示例 -->
        <section class="demo-section">
            <h2 class="demo-title">按钮组件示例</h2>
            <div class="u-flex u-gap-md u-flex-wrap">
                <button class="btn btn--primary">主要按钮</button>
                <button class="btn btn--secondary">次要按钮</button>
                <button class="btn btn--outline">轮廓按钮</button>
                <button class="btn btn--ghost">幽灵按钮</button>
                <button class="btn btn--danger">危险按钮</button>
                <button class="btn btn--success">成功按钮</button>
            </div>
            
            <div class="u-flex u-gap-md u-flex-wrap u-mt-md">
                <button class="btn btn--primary btn--xs">超小按钮</button>
                <button class="btn btn--primary btn--sm">小按钮</button>
                <button class="btn btn--primary">默认按钮</button>
                <button class="btn btn--primary btn--lg">大按钮</button>
            </div>
        </section>

        <!-- 输入框组件示例 -->
        <section class="demo-section">
            <h2 class="demo-title">输入框组件示例</h2>
            <div class="u-grid u-gap-md" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));">
                <div>
                    <label class="u-block u-mb-xs u-text-sm u-font-medium">普通输入框</label>
                    <input type="text" class="input" placeholder="请输入内容">
                </div>
                <div>
                    <label class="u-block u-mb-xs u-text-sm u-font-medium">错误状态</label>
                    <input type="text" class="input input--error" placeholder="错误状态">
                </div>
                <div>
                    <label class="u-block u-mb-xs u-text-sm u-font-medium">成功状态</label>
                    <input type="text" class="input input--success" placeholder="成功状态">
                </div>
                <div>
                    <label class="u-block u-mb-xs u-text-sm u-font-medium">禁用状态</label>
                    <input type="text" class="input" placeholder="禁用状态" disabled>
                </div>
            </div>
            
            <div class="u-mt-md">
                <label class="u-block u-mb-xs u-text-sm u-font-medium">文本域</label>
                <textarea class="textarea" placeholder="请输入多行文本内容"></textarea>
            </div>
        </section>

        <!-- 卡片组件示例 -->
        <section class="demo-section">
            <h2 class="demo-title">卡片组件示例</h2>
            <div class="u-grid u-gap-md" style="grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));">
                <div class="card">
                    <div class="card__header">
                        <h3 class="u-m-0 u-font-medium">卡片标题</h3>
                    </div>
                    <div class="card__body">
                        <p class="u-text-secondary u-mb-md">这是卡片的主要内容区域，可以包含任何内容。</p>
                        <div class="u-flex u-gap-sm">
                            <span class="badge badge--primary">标签1</span>
                            <span class="badge badge--secondary">标签2</span>
                        </div>
                    </div>
                    <div class="card__footer">
                        <div class="u-flex u-justify-end u-gap-sm">
                            <button class="btn btn--ghost btn--sm">取消</button>
                            <button class="btn btn--primary btn--sm">确认</button>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card__body">
                        <h3 class="u-mt-0 u-font-medium">简单卡片</h3>
                        <p class="u-text-secondary">没有头部和底部的简单卡片示例。</p>
                        <div class="progress u-mt-md">
                            <div class="progress__bar" style="width: 65%;"></div>
                        </div>
                        <p class="u-text-xs u-text-muted u-mt-xs">进度: 65%</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 工具类示例 -->
        <section class="demo-section">
            <h2 class="demo-title">工具类示例</h2>
            <div class="u-grid u-gap-md" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
                <div class="u-p-md u-bg-success u-text-white u-rounded-lg u-text-center">
                    <div class="u-text-lg u-font-bold">成功状态</div>
                    <div class="u-text-sm u-opacity-50">使用工具类</div>
                </div>
                <div class="u-p-md u-bg-warning u-text-white u-rounded-lg u-text-center">
                    <div class="u-text-lg u-font-bold">警告状态</div>
                    <div class="u-text-sm u-opacity-50">快速样式</div>
                </div>
                <div class="u-p-md u-bg-danger u-text-white u-rounded-lg u-text-center">
                    <div class="u-text-lg u-font-bold">危险状态</div>
                    <div class="u-text-sm u-opacity-50">原子化CSS</div>
                </div>
                <div class="u-p-md u-bg-info u-text-white u-rounded-lg u-text-center">
                    <div class="u-text-lg u-font-bold">信息状态</div>
                    <div class="u-text-sm u-opacity-50">一致性设计</div>
                </div>
            </div>
        </section>

        <!-- 交互示例 -->
        <section class="demo-section">
            <h2 class="demo-title">交互效果示例</h2>
            <div class="u-flex u-gap-md u-flex-wrap">
                <button id="loading-demo" class="btn btn--primary">
                    显示加载状态
                </button>
                <button id="opacity-demo" class="btn btn--secondary">
                    透明度动画
                </button>
                <button id="transform-demo" class="btn btn--outline">
                    变换动画
                </button>
                <button id="modal-demo" class="btn btn--ghost">
                    打开模态框
                </button>
            </div>
            
            <div id="demo-target" class="u-mt-md u-p-md u-bg-tertiary u-rounded-lg u-text-center">
                <div class="u-text-lg u-font-medium">动画目标元素</div>
                <div class="u-text-sm u-text-secondary">点击上方按钮查看效果</div>
            </div>
        </section>
    </div>

    <!-- 模态框示例 -->
    <div id="demo-modal" class="modal u-hidden">
        <div class="modal__backdrop"></div>
        <div class="modal__content">
            <div class="modal__header">
                <h3 class="modal__title">示例模态框</h3>
                <button class="modal__close" id="modal-close">&times;</button>
            </div>
            <div class="modal__body">
                <p>这是一个使用新组件系统构建的模态框示例。</p>
                <p class="u-text-secondary">所有样式都使用CSS变量和组件类，没有内联样式或!important声明。</p>
            </div>
            <div class="modal__footer">
                <button class="btn btn--ghost" id="modal-cancel">取消</button>
                <button class="btn btn--primary" id="modal-confirm">确认</button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="static/js/style-utils.js"></script>
    <script>
        // 示例：使用StyleUtils替代内联样式操作
        document.addEventListener('DOMContentLoaded', function() {
            // 主题切换
            document.getElementById('theme-toggle').addEventListener('click', function() {
                document.body.classList.toggle('dark-theme');
            });

            // 加载状态示例
            document.getElementById('loading-demo').addEventListener('click', function() {
                const target = document.getElementById('demo-target');
                StyleUtils.setLoading(target, true);
                
                setTimeout(() => {
                    StyleUtils.setLoading(target, false);
                }, 2000);
            });

            // 透明度动画示例
            document.getElementById('opacity-demo').addEventListener('click', function() {
                const target = document.getElementById('demo-target');
                StyleUtils.setOpacity(target, 0);
                
                setTimeout(() => {
                    StyleUtils.setOpacity(target, 1);
                }, 1000);
            });

            // 变换动画示例
            document.getElementById('transform-demo').addEventListener('click', function() {
                const target = document.getElementById('demo-target');
                StyleUtils.setTransform(target, 'scale-95');
                
                setTimeout(() => {
                    StyleUtils.setTransform(target, 'scale-100');
                }, 500);
            });

            // 模态框示例
            document.getElementById('modal-demo').addEventListener('click', function() {
                StyleUtils.show(document.getElementById('demo-modal'), 'flex');
                StyleUtils.animate(document.querySelector('.modal__content'), 'fade-in');
            });

            document.getElementById('modal-close').addEventListener('click', closeModal);
            document.getElementById('modal-cancel').addEventListener('click', closeModal);
            document.getElementById('modal-confirm').addEventListener('click', closeModal);

            function closeModal() {
                StyleUtils.hide(document.getElementById('demo-modal'));
            }

            // 点击背景关闭模态框
            document.querySelector('.modal__backdrop').addEventListener('click', closeModal);
        });
    </script>
</body>
</html>
