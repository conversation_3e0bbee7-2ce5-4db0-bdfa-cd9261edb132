# -*- coding: utf-8 -*-
"""
存储 AI 模型和语音选项数据
基于提供的 models.json 和用户需求进行更新
支持优先本地加载，然后网络API更新的机制
"""
import logging
import requests
import json
import time
from typing import Dict, List, Optional, Any
from threading import Lock

# 文本生成模型列表 (结合 models.json 和原有列表)
# 添加了 provider, input_modalities, output_modalities, vision, audio 等属性
# Standardized structure, added 'supports_functions' flag where known/applicable
TEXT_MODELS = [
    {"id": "openai", "name": "OpenAI GPT-4.1-nano", "description": "快速、通用，支持视觉", "provider":"Azure", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "supports_functions": True},
    {"id": "openai-large", "name": "OpenAI GPT-4.1 mini", "description": "更强能力，支持视觉", "provider":"Azure", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "supports_functions": True},
    {"id": "openai-reasoning", "name": "OpenAI o4-mini", "description": "推理优化，支持视觉", "provider":"Azure", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "reasoning": True, "supports_functions": True},
    {"id": "qwen-coder", "name": "Qwen 2.5 Coder 32B", "description": "代码生成优化", "provider":"Scaleway", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "llama", "name": "Llama 3.3 70B", "description": "大型通用模型", "provider":"Cloudflare", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "llamascout", "name": "Llama 4 Scout 17B", "description": "中型 Llama 模型", "provider":"Cloudflare", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "mistral", "name": "Mistral Small 3", "description": "高效 Mistral 模型，支持视觉", "provider":"Cloudflare", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "supports_functions": True}, # Mistral often supports functions
    {"id": "unity", "name": "Unity Mistral Large", "description": "大型 Mistral，支持视觉，无审查", "provider":"Scaleway", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "uncensored": True, "supports_functions": True}, # Assume large Mistral supports functions
    {"id": "midijourney", "name": "Midijourney", "description": "创意文本生成", "provider":"Azure", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "rtist", "name": "Rtist", "description": "艺术风格文本生成", "provider":"Azure", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "searchgpt", "name": "SearchGPT", "description": "结合搜索的问答，支持视觉", "provider":"Azure", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "supports_functions": False}, # Search might be implicit, not function call
    {"id": "evil", "name": "Evil", "description": "无审查模型，支持视觉", "provider":"Scaleway", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "uncensored": True, "supports_functions": False},
    {"id": "deepseek-reasoning", "name": "DeepSeek-R1 Distill Qwen 32B", "description": "DeepSeek 推理模型", "provider":"Cloudflare", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "reasoning": True, "supports_functions": False},
    {"id": "deepseek-reasoning-large", "name": "DeepSeek R1 - Llama 70B", "description": "大型 DeepSeek 推理模型", "provider":"Scaleway", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "reasoning": True, "supports_functions": False},
    {"id": "phi", "name": "Phi-4 Instruct", "description": "微软 Phi 模型，支持视觉/音频输入", "provider":"Cloudflare", "input_modalities":["text","image","audio"], "output_modalities":["text"], "vision":True, "audio":True, "supports_functions": True}, # Phi models often support functions
    {"id": "llama-vision", "name": "Llama 3.2 11B Vision", "description": "Llama 视觉模型", "provider":"Cloudflare", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "supports_functions": False},
    {"id": "gemini", "name": "gemini-2.5-flash-preview-04-17", "description": "谷歌 Gemini Flash，支持视觉/音频", "provider":"Azure", "input_modalities":["text","image","audio"], "output_modalities":["audio","text"], "vision":True, "audio":True, "supports_functions": True}, # Gemini supports functions
    {"id": "hormoz", "name": "Hormoz 8b", "description": "Hormoz 8b 模型", "provider":"Modal", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "hypnosis-tracy", "name": "Hypnosis Tracy 7B", "description": "支持音频输入/输出", "provider":"Azure", "input_modalities":["text","audio"], "output_modalities":["audio","text"], "vision":False, "audio":True, "supports_functions": False},
    {"id": "deepseek", "name": "DeepSeek-V3", "description": "DeepSeek V3 模型", "provider":"DeepSeek", "input_modalities":["text"], "output_modalities":["text"], "vision":False, "audio":False, "supports_functions": False},
    {"id": "sur", "name": "Sur AI Assistant (Mistral)", "description": "Sur AI 助手，支持视觉", "provider":"Scaleway", "input_modalities":["text","image"], "output_modalities":["text"], "vision":True, "audio":False, "supports_functions": True}, # Based on Mistral
    {"id": "openai-audio", "name": "OpenAI GPT-4o-audio-preview", "description": "OpenAI 音频模型 (STT/TTS)", "provider":"Azure", "input_modalities":["text","image","audio"], "output_modalities":["audio","text"], "vision":True, "audio":True, "supports_functions": True} # Used for STT/TTS via compatible endpoint
]

# Pollinations 图像生成模型列表
IMAGE_MODELS = [
    {"id": "auto", "name": "Auto", "description": "AI 自动根据提示词选择最合适的模型"},
    {"id": "flux", "name": "Flux", "description": "默认 Flux 模型"},
    {"id": "flux-pro", "name": "Flux Pro", "description": "高质量 Pro 模型"},
    {"id": "flux-realism", "name": "Flux Realism", "description": "写实风格"},
    {"id": "flux-anime", "name": "Flux Anime", "description": "动漫风格"},
    {"id": "flux-3d", "name": "Flux 3D", "description": "3D 渲染风格"},
    {"id": "flux-cablyai", "name": "Flux Cablyai", "description": "Cablyai 艺术风格"},
    {"id": "flux-schnell", "name": "Flux Schnell", "description": "快速生成"},
    {"id": "turbo", "name": "Turbo", "description": "Turbo 图像模型"},
    # 可以根据 https://image.pollinations.ai/models 或文档更新
]

# Pollinations 音频/语音模型 (主要是 TTS 语音选项 for openai-audio model)
# V48: Added descriptions where possible, grouped slightly
AUDIO_VOICES = [
    # Standard OpenAI Voices
    {"id": "alloy", "name": "Alloy", "description": "标准男声"},
    {"id": "echo", "name": "Echo", "description": "标准男声"},
    {"id": "fable", "name": "Fable", "description": "故事叙述男声"},
    {"id": "onyx", "name": "Onyx", "description": "深沉男声"},
    {"id": "nova", "name": "Nova", "description": "标准女声"},
    {"id": "shimmer", "name": "Shimmer", "description": "标准女声"},
    # Additional Voices (May vary in quality/availability)
    {"id": "coral", "name": "Coral", "description": "女声"},
    {"id": "verse", "name": "Verse", "description": "男声"},
    {"id": "ballad", "name": "Ballad", "description": "男声"},
    {"id": "ash", "name": "Ash", "description": "男声"},
    {"id": "sage", "name": "Sage", "description": "女声"},
    {"id": "amuch", "name": "Amuch", "description": "未知特性"},
    {"id": "aster", "name": "Aster", "description": "未知特性"},
    {"id": "brook", "name": "Brook", "description": "未知特性"},
    {"id": "clover", "name": "Clover", "description": "未知特性"},
    {"id": "dan", "name": "Dan", "description": "未知特性"},
    {"id": "elan", "name": "Elan", "description": "未知特性"},
    {"id": "marilyn", "name": "Marilyn", "description": "未知特性"},
    {"id": "meadow", "name": "Meadow", "description": "未知特性"},
]

# Helper functions with network update support
def get_text_models(force_update: bool = False) -> List[Dict[str, Any]]:
    """
    返回文本模型列表
    优先返回本地配置，然后尝试从网络更新

    Args:
        force_update: 是否强制从网络更新

    Returns:
        合并后的文本模型列表
    """
    # 尝试网络更新（如果需要）
    if force_update:
        _update_models_from_network()
    else:
        # 后台异步更新，不阻塞当前请求
        try:
            import threading
            threading.Thread(target=_update_models_from_network, daemon=True).start()
        except Exception as e:
            logging.debug(f"Failed to start background update thread: {e}")

    # 合并本地和网络模型
    network_models = _network_models_cache.get('text_models', [])
    return _merge_models(TEXT_MODELS, network_models)

def get_image_models(force_update: bool = False) -> List[Dict[str, Any]]:
    """
    返回图像模型列表
    优先返回本地配置，然后尝试从网络更新

    Args:
        force_update: 是否强制从网络更新

    Returns:
        合并后的图像模型列表
    """
    # 尝试网络更新（如果需要）
    if force_update:
        _update_models_from_network()
    else:
        # 后台异步更新，不阻塞当前请求
        try:
            import threading
            threading.Thread(target=_update_models_from_network, daemon=True).start()
        except Exception as e:
            logging.debug(f"Failed to start background update thread: {e}")

    # 合并本地和网络模型
    network_models = _network_models_cache.get('image_models', [])
    return _merge_models(IMAGE_MODELS, network_models)

def get_audio_voices(force_update: bool = False) -> List[Dict[str, Any]]:
    """
    返回音频语音列表
    优先返回本地配置，然后尝试从网络更新

    Args:
        force_update: 是否强制从网络更新

    Returns:
        合并后的音频语音列表
    """
    # 尝试网络更新（如果需要）
    if force_update:
        _update_models_from_network()
    else:
        # 后台异步更新，不阻塞当前请求
        try:
            import threading
            threading.Thread(target=_update_models_from_network, daemon=True).start()
        except Exception as e:
            logging.debug(f"Failed to start background update thread: {e}")

    # 合并本地和网络模型
    network_models = _network_models_cache.get('audio_voices', [])
    return _merge_models(AUDIO_VOICES, network_models)

# 兼容性函数，保持向后兼容
def get_text_models_local_only():
    """仅返回本地文本模型列表"""
    return TEXT_MODELS

def get_image_models_local_only():
    """仅返回本地图像模型列表"""
    return IMAGE_MODELS

def get_audio_voices_local_only():
    """仅返回本地音频语音列表"""
    return AUDIO_VOICES

# V48: Cache for model capabilities to avoid repeated lookups
_model_capabilities_cache = {}

# 网络模型更新相关配置
_update_lock = Lock()
_last_update_time = 0
_update_interval = 3600  # 1小时更新一次
_network_models_cache = {
    'text_models': [],
    'image_models': [],
    'audio_voices': []
}

# API端点配置
POLLINATIONS_TEXT_MODELS_URL = "https://text.pollinations.ai/models"
POLLINATIONS_IMAGE_MODELS_URL = "https://image.pollinations.ai/models"

def _fetch_network_models(url: str, model_type: str, timeout: int = 10) -> List[Dict[str, Any]]:
    """从网络API获取模型列表"""
    try:
        logging.info(f"Fetching {model_type} models from: {url}")
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()

        data = response.json()

        if model_type == 'text':
            return _parse_text_models_from_api(data)
        elif model_type == 'image':
            return _parse_image_models_from_api(data)
        elif model_type == 'audio':
            return _parse_audio_voices_from_api(data)
        else:
            logging.warning(f"Unknown model type: {model_type}")
            return []

    except requests.exceptions.RequestException as e:
        logging.error(f"Network error fetching {model_type} models: {e}")
        return []
    except json.JSONDecodeError as e:
        logging.error(f"JSON decode error for {model_type} models: {e}")
        return []
    except Exception as e:
        logging.error(f"Unexpected error fetching {model_type} models: {e}")
        return []

def _parse_text_models_from_api(data: Any) -> List[Dict[str, Any]]:
    """解析文本模型API响应"""
    models = []
    try:
        if isinstance(data, list):
            for item in data:
                if isinstance(item, dict):
                    model_id = item.get('id') or item.get('name')
                    if model_id:
                        model = {
                            'id': model_id,
                            'name': item.get('name', model_id),
                            'description': item.get('description', ''),
                            'provider': item.get('provider', 'Pollinations'),
                            'input_modalities': item.get('input_modalities', ['text']),
                            'output_modalities': item.get('output_modalities', ['text']),
                            'vision': item.get('vision', False),
                            'audio': item.get('audio', False),
                            'supports_functions': item.get('supports_functions', False)
                        }
                        models.append(model)
        elif isinstance(data, dict):
            # 处理可能的嵌套结构
            if 'models' in data:
                return _parse_text_models_from_api(data['models'])
            elif 'text_models' in data:
                return _parse_text_models_from_api(data['text_models'])

        logging.info(f"Parsed {len(models)} text models from API")
        return models

    except Exception as e:
        logging.error(f"Error parsing text models from API: {e}")
        return []

def _parse_image_models_from_api(data: Any) -> List[Dict[str, Any]]:
    """解析图像模型API响应"""
    models = []
    try:
        if isinstance(data, list):
            for item in data:
                if isinstance(item, str):
                    # 简单字符串列表
                    models.append({
                        'id': item,
                        'name': item.replace('-', ' ').title(),
                        'description': f'{item} 图像生成模型'
                    })
                elif isinstance(item, dict):
                    model_id = item.get('id') or item.get('name')
                    if model_id:
                        models.append({
                            'id': model_id,
                            'name': item.get('name', model_id.replace('-', ' ').title()),
                            'description': item.get('description', f'{model_id} 图像生成模型')
                        })
        elif isinstance(data, dict):
            if 'models' in data:
                return _parse_image_models_from_api(data['models'])
            elif 'image_models' in data:
                return _parse_image_models_from_api(data['image_models'])

        logging.info(f"Parsed {len(models)} image models from API")
        return models

    except Exception as e:
        logging.error(f"Error parsing image models from API: {e}")
        return []

def _parse_audio_voices_from_api(data: Any) -> List[Dict[str, Any]]:
    """解析音频语音API响应"""
    voices = []
    try:
        if isinstance(data, list):
            for item in data:
                if isinstance(item, str):
                    voices.append({
                        'id': item,
                        'name': item.title(),
                        'description': f'{item} 语音'
                    })
                elif isinstance(item, dict):
                    voice_id = item.get('id') or item.get('name')
                    if voice_id:
                        voices.append({
                            'id': voice_id,
                            'name': item.get('name', voice_id.title()),
                            'description': item.get('description', f'{voice_id} 语音')
                        })
        elif isinstance(data, dict):
            # 查找可能的语音列表
            for key in ['voices', 'audio_voices', 'tts_voices', 'models']:
                if key in data:
                    return _parse_audio_voices_from_api(data[key])

        logging.info(f"Parsed {len(voices)} audio voices from API")
        return voices

    except Exception as e:
        logging.error(f"Error parsing audio voices from API: {e}")
        return []

def _update_models_from_network() -> bool:
    """从网络更新模型列表"""
    global _last_update_time, _network_models_cache

    with _update_lock:
        current_time = time.time()

        # 检查是否需要更新
        if current_time - _last_update_time < _update_interval:
            return False

        logging.info("Starting network models update...")

        # 获取文本模型
        text_models = _fetch_network_models(POLLINATIONS_TEXT_MODELS_URL, 'text')
        if text_models:
            _network_models_cache['text_models'] = text_models

        # 获取图像模型
        image_models = _fetch_network_models(POLLINATIONS_IMAGE_MODELS_URL, 'image')
        if image_models:
            _network_models_cache['image_models'] = image_models

        # 音频语音通常从文本模型API获取
        audio_voices = _fetch_network_models(POLLINATIONS_TEXT_MODELS_URL, 'audio')
        if audio_voices:
            _network_models_cache['audio_voices'] = audio_voices

        _last_update_time = current_time
        logging.info("Network models update completed")
        return True

def _merge_models(local_models: List[Dict], network_models: List[Dict]) -> List[Dict]:
    """合并本地和网络模型列表，优先保留本地配置"""
    if not network_models:
        return local_models

    # 创建本地模型ID集合
    local_ids = {model['id'] for model in local_models}

    # 合并列表：本地模型 + 新的网络模型
    merged = local_models.copy()

    for network_model in network_models:
        if network_model['id'] not in local_ids:
            merged.append(network_model)

    return merged

def get_model_capabilities(model_id):
    """根据模型 ID 获取模型的能力 (带缓存)"""
    if not model_id: # Handle case where model_id might be None or empty
        return {'vision': False, 'audio_input': False, 'audio_output': False, 'supports_functions': False}

    if model_id in _model_capabilities_cache:
        return _model_capabilities_cache[model_id]

    # Search in both local and network models
    all_text_models = get_text_models()
    for model in all_text_models:
        if model['id'] == model_id:
            capabilities = {
                'vision': model.get('vision', False),
                # Check if 'audio' is in input_modalities list
                'audio_input': 'audio' in model.get('input_modalities', []),
                # Check if 'audio' is in output_modalities list
                'audio_output': 'audio' in model.get('output_modalities', []),
                'supports_functions': model.get('supports_functions', False)
            }
            _model_capabilities_cache[model_id] = capabilities
            return capabilities

    # If model_id not found, return default capabilities
    logging.warning(f"Capabilities not found for model_id '{model_id}'. Returning default capabilities.")
    default_caps = {'vision': False, 'audio_input': False, 'audio_output': False, 'supports_functions': False}
    _model_capabilities_cache[model_id] = default_caps # Cache default for unknown models too
    return default_caps

# 额外的实用函数
def force_update_models() -> Dict[str, bool]:
    """
    强制从网络更新所有模型列表

    Returns:
        更新结果字典
    """
    result = {
        'text_models': False,
        'image_models': False,
        'audio_voices': False,
        'success': False
    }

    try:
        # 强制更新
        updated = _update_models_from_network()
        if updated:
            result['text_models'] = len(_network_models_cache.get('text_models', [])) > 0
            result['image_models'] = len(_network_models_cache.get('image_models', [])) > 0
            result['audio_voices'] = len(_network_models_cache.get('audio_voices', [])) > 0
            result['success'] = any([result['text_models'], result['image_models'], result['audio_voices']])

        logging.info(f"Force update result: {result}")
        return result

    except Exception as e:
        logging.error(f"Error in force_update_models: {e}")
        return result

def get_models_status() -> Dict[str, Any]:
    """
    获取模型列表状态信息

    Returns:
        状态信息字典
    """
    return {
        'last_update_time': _last_update_time,
        'update_interval': _update_interval,
        'local_models_count': {
            'text': len(TEXT_MODELS),
            'image': len(IMAGE_MODELS),
            'audio': len(AUDIO_VOICES)
        },
        'network_models_count': {
            'text': len(_network_models_cache.get('text_models', [])),
            'image': len(_network_models_cache.get('image_models', [])),
            'audio': len(_network_models_cache.get('audio_voices', []))
        },
        'merged_models_count': {
            'text': len(get_text_models_local_only()) + len(_network_models_cache.get('text_models', [])),
            'image': len(get_image_models_local_only()) + len(_network_models_cache.get('image_models', [])),
            'audio': len(get_audio_voices_local_only()) + len(_network_models_cache.get('audio_voices', []))
        }
    }

def clear_models_cache():
    """清除模型缓存"""
    global _network_models_cache, _model_capabilities_cache, _last_update_time

    with _update_lock:
        _network_models_cache = {
            'text_models': [],
            'image_models': [],
            'audio_voices': []
        }
        _model_capabilities_cache.clear()
        _last_update_time = 0

    logging.info("Models cache cleared")

def set_update_interval(seconds: int):
    """设置模型更新间隔"""
    global _update_interval
    if seconds > 0:
        _update_interval = seconds
        logging.info(f"Update interval set to {seconds} seconds")
    else:
        logging.warning("Invalid update interval, must be positive")

# 初始化时尝试加载网络模型（非阻塞）
def _initialize_network_models():
    """初始化时异步加载网络模型"""
    try:
        import threading
        threading.Thread(target=_update_models_from_network, daemon=True).start()
        logging.info("Started background initialization of network models")
    except Exception as e:
        logging.debug(f"Failed to start background initialization: {e}")

# 模块加载时自动初始化
_initialize_network_models()
