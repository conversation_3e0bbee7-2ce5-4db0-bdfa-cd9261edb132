# === 生产依赖 ===
# Flask - Web 框架核心（固定安全版本）
flask==3.0.3

# requests - HTTP 请求库（固定安全版本）
requests==2.32.3

# Werkzeug - Flask 底层 WSGI 工具（安全版本）
werkzeug==3.0.4

# Jinja2 - 模板引擎（安全版本）
jinja2==3.1.4

# MarkupSafe - 安全的字符串处理（安全版本）
markupsafe==2.1.5

# === 开发/调试依赖 ===
# python-dotenv - 环境变量管理（可选）
python-dotenv==1.0.1

# === 安全相关依赖 ===
# cryptography - 加密库（用于安全密钥生成）
cryptography>=42.0.0

# === 标准库（无需安装） ===
# logging - Python 标准库
# os - Python 标准库
# json - Python 标准库
# base64 - Python 标准库
# urllib - Python 标准库
# io - Python 标准库
# threading - Python 标准库
# time - Python 标准库
# re - Python 标准库
# secrets - Python 标准库（Python 3.6+）

# === 安全说明 ===
# 所有版本都已固定到已知安全的版本
# 定期检查安全更新：pip-audit 或 safety check
# 生产环境建议使用虚拟环境隔离依赖
