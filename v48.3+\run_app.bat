@echo off
REM 运行Flask应用的批处理脚本
REM 自动激活虚拟环境并启动应用

echo 正在启动Flask应用...
echo.

REM 检查虚拟环境是否存在
if not exist ".venv\Scripts\activate.bat" (
    echo 错误：虚拟环境不存在！
    echo 请先创建虚拟环境：python -m venv .venv
    pause
    exit /b 1
)

REM 检查app.py是否存在
if not exist "app.py" (
    echo 错误：app.py文件不存在！
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 激活虚拟环境
call .venv\Scripts\activate.bat

REM 显示环境信息
echo 虚拟环境已激活
echo Python版本：
python --version
echo.

REM 安装依赖（如果需要）
if exist "requirements.txt" (
    echo 检查并安装依赖...
    pip install -r requirements.txt
    echo.
)

REM 启动Flask应用
echo 启动Flask应用...
echo 应用将在 http://localhost:5000 运行
echo 按 Ctrl+C 停止应用
echo.
python app.py

pause
