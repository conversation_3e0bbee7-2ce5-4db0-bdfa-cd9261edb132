/* ===================================================================
   组件系统 - 统一的UI组件样式
   ================================================================= */

/* === 按钮组件 === */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-tight);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    user-select: none;
    white-space: nowrap;
}

.btn:focus {
    outline: none;
    border-color: var(--focus-border-color);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 按钮变体 */
.btn--primary {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
    color: var(--color-white);
}

.btn--primary:hover:not(:disabled) {
    background-color: var(--color-primary-hover);
    border-color: var(--color-primary-hover);
}

.btn--primary:active {
    background-color: var(--color-primary-active);
    border-color: var(--color-primary-active);
    transform: scale(0.98);
}

.btn--secondary {
    background-color: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.btn--secondary:hover:not(:disabled) {
    background-color: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

.btn--outline {
    background-color: transparent;
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.btn--outline:hover:not(:disabled) {
    background-color: var(--bg-secondary);
    border-color: var(--border-secondary);
}

.btn--ghost {
    background-color: transparent;
    border-color: transparent;
    color: var(--text-secondary);
}

.btn--ghost:hover:not(:disabled) {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.btn--danger {
    background-color: var(--color-danger);
    border-color: var(--color-danger);
    color: var(--color-white);
}

.btn--danger:hover:not(:disabled) {
    background-color: var(--color-danger-hover);
    border-color: var(--color-danger-hover);
}

.btn--success {
    background-color: var(--color-success);
    border-color: var(--color-success);
    color: var(--color-white);
}

.btn--success:hover:not(:disabled) {
    background-color: var(--color-success-hover);
    border-color: var(--color-success-hover);
}

/* 按钮尺寸 */
.btn--xs {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.btn--sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.btn--lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-lg);
}

.btn--xl {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-xl);
}

/* === 输入框组件 === */
.input {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.input:focus {
    outline: none;
    border-color: var(--focus-border-color);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
}

.input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: var(--bg-secondary);
}

.input--error {
    border-color: var(--color-danger);
}

.input--error:focus {
    border-color: var(--color-danger);
    box-shadow: 0 0 0 var(--focus-glow-radius) rgba(239, 68, 68, 0.25);
}

.input--success {
    border-color: var(--color-success);
}

/* === 文本域组件 === */
.textarea {
    display: block;
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    resize: vertical;
    transition: var(--transition-fast);
}

.textarea:focus {
    outline: none;
    border-color: var(--focus-border-color);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
}

/* === 卡片组件 === */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card__header {
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.card__body {
    padding: var(--spacing-lg);
}

.card__footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

/* === 模态框组件 === */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: var(--z-modal);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.modal__backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-overlay);
    backdrop-filter: blur(4px);
}

.modal__content {
    position: relative;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
}

.modal__header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal__title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.modal__close {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-base);
    transition: var(--transition-fast);
}

.modal__close:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.modal__body {
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.modal__footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* === 下拉菜单组件 === */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown__trigger {
    background: none;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: var(--transition-fast);
}

.dropdown__trigger:hover {
    background-color: var(--bg-secondary);
    border-color: var(--border-secondary);
}

.dropdown__menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: var(--z-dropdown);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    margin-top: var(--spacing-xs);
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-8px);
    transition: var(--transition-fast);
}

.dropdown--open .dropdown__menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown__item {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    transition: var(--transition-fast);
}

.dropdown__item:hover {
    background-color: var(--bg-secondary);
}

.dropdown__item:focus {
    outline: none;
    background-color: var(--bg-secondary);
}

.dropdown__item--active {
    background-color: var(--color-primary-light);
    color: var(--color-primary);
}

/* === 标签组件 === */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-full);
    white-space: nowrap;
}

.badge--primary {
    background-color: var(--color-primary-light);
    color: var(--color-primary);
}

.badge--secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

.badge--success {
    background-color: var(--color-success-light);
    color: var(--color-success);
}

.badge--warning {
    background-color: var(--color-warning-light);
    color: var(--color-warning);
}

.badge--danger {
    background-color: var(--color-danger-light);
    color: var(--color-danger);
}

/* === 加载状态组件 === */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

.loading-spinner--sm {
    width: 12px;
    height: 12px;
    border-width: 1.5px;
}

.loading-spinner--lg {
    width: 24px;
    height: 24px;
    border-width: 3px;
}

/* === 进度条组件 === */
.progress {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress__bar {
    height: 100%;
    background-color: var(--color-primary);
    border-radius: var(--radius-full);
    transition: width var(--duration-normal) var(--ease-out);
}

.progress--sm {
    height: 4px;
}

.progress--lg {
    height: 12px;
}

/* === 分隔线组件 === */
.divider {
    border: none;
    height: 1px;
    background-color: var(--border-primary);
    margin: var(--spacing-md) 0;
}

.divider--vertical {
    width: 1px;
    height: auto;
    margin: 0 var(--spacing-md);
}

/* === 工具提示组件 === */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip__content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--color-gray-900);
    color: var(--color-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-base);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    z-index: var(--z-tooltip);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    margin-bottom: var(--spacing-xs);
}

.tooltip:hover .tooltip__content {
    opacity: 1;
    visibility: visible;
}

.tooltip__content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--color-gray-900);
}

/* === 状态类 === */
.is-active { }
.is-disabled { opacity: 0.6; pointer-events: none; }
.is-loading { position: relative; pointer-events: none; }
.is-hidden { display: none; }
.is-visible { display: block; }
.is-collapsed { display: none; }
.is-expanded { display: block; }

/* === 动画类 === */
.animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-out);
}

.animate-fade-out {
    animation: fadeOut var(--duration-normal) var(--ease-out);
}

.animate-slide-up {
    animation: slideUp var(--duration-normal) var(--ease-out);
}

.animate-slide-down {
    animation: slideDown var(--duration-normal) var(--ease-out);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideUp {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
