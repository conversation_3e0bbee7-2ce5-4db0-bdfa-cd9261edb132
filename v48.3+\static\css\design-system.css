/* ===================================================================
   统一设计系统 - 现代极简风格

   作用范围：
   - 为整个应用提供统一的设计令牌（间距、圆角、字体、动画等）
   - 定义明亮主题和暗黑主题的基础颜色变量
   - 提供统一的按钮悬停效果和交互样式
   - 服务于所有主题，确保设计一致性

   注意：此文件同时服务于明亮主题和暗黑主题，不应删除
   ================================================================= */

/* === 根级设计令牌 - 所有主题共享的基础规格 === */
:root {
    /* === 间距系统 === */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;
    --spacing-4xl: 40px;
    --spacing-5xl: 48px;

    /* === 圆角系统 === */
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    --radius-3xl: 20px;
    --radius-full: 9999px;

    /* === 字体系统 === */
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 30px;
    --font-size-4xl: 36px;

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* === 层级系统 === */
    --z-background: -1;
    --z-base: 0;
    --z-content: 1;
    --z-elevated: 10;
    --z-floating: 20;
    --z-overlay: 30;
    --z-modal: 40;
    --z-tooltip: 50;
    --z-notification: 60;
    --z-maximum: 9999;

    /* === 动画系统 === */
    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;
    --duration-slower: 500ms;

    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-natural: cubic-bezier(0.25, 0.8, 0.25, 1);

    /* === 阴影系统 === */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

    /* === 模糊效果系统 === */
    --blur-sm: blur(4px);
    --blur-md: blur(8px);
    --blur-lg: blur(12px);
    --blur-xl: blur(16px);
    --blur-2xl: blur(24px);

    /* === Mermaid组件系统变量 === */
    --mermaid-container-margin: 0.8em 0;
    --mermaid-diagram-padding: 10px;
    --mermaid-diagram-border-radius: 6px;
    --mermaid-diagram-min-height: 45px;
    --mermaid-action-button-size: 32px;
    --mermaid-action-button-gap: 6px;
    --mermaid-action-button-padding: 6px 10px;
    --mermaid-actions-position-top: 8px;
    --mermaid-actions-position-right: 8px;

    /* === 明亮主题颜色系统 === */
    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;
    --color-primary-active: #1d4ed8;
    
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-danger: #ef4444;
    --color-danger-hover: #dc2626;
    --color-danger-active: #b91c1c;
    --color-danger-dark: #991b1b;
    --color-danger-light: rgba(239, 68, 68, 0.1);
    --color-info: #06b6d4;

    /* 背景色 - 优化明亮主题配色层次 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-surface: #ffffff;
    --bg-elevated: #ffffff;
    --bg-overlay: rgba(248, 250, 252, 0.98);

    /* 文字色 - 优化明亮主题配色层次 */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-tertiary: #64748b;
    --text-muted: #94a3b8;
    --text-inverse: #ffffff;
    --text-link: #3b82f6;
    --text-link-hover: #2563eb;

    /* 边框色 - 优化对比度和层次感 */
    --border-primary: #d1d5db;
    --border-secondary: #9ca3af;
    --border-tertiary: #6b7280;
    --border-focus: #3b82f6;

    /* 灰色系统 - 明亮主题 */
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;

    /* 透明度变量 */
    --alpha-5: 0.05;
    --alpha-10: 0.1;
    --alpha-15: 0.15;
    --alpha-20: 0.2;
    --alpha-25: 0.25;
    --alpha-30: 0.3;
    --alpha-40: 0.4;
    --alpha-50: 0.5;
    --alpha-60: 0.6;
    --alpha-70: 0.7;
    --alpha-80: 0.8;
    --alpha-90: 0.9;
    --alpha-95: 0.95;

    /* === 组件特定变量 === */
    /* 输入框 - 优化明亮主题配色 */
    --input-bg: rgba(255, 255, 255, 0.98);
    --input-border: var(--border-primary);
    --input-border-focus: var(--border-focus);
    --input-text: var(--text-primary);
    --input-placeholder: var(--text-muted);
    --input-padding: var(--spacing-md) var(--spacing-lg);
    --input-radius: var(--radius-xl);

    /* 按钮 */
    --button-padding: var(--spacing-sm) var(--spacing-lg);
    --button-radius: var(--radius-lg);
    --button-font-weight: var(--font-weight-medium);
    --button-transition: all var(--duration-fast) var(--ease-out);

    /* 卡片 */
    --card-bg: var(--bg-surface);
    --card-border: var(--border-primary);
    --card-radius: var(--radius-xl);
    --card-padding: var(--spacing-xl);
    --card-shadow: var(--shadow-sm);

    /* 工具栏 - 优化配色协调性 */
    --toolbar-bg: rgba(248, 250, 252, 0.95);
    --toolbar-border: rgba(209, 213, 219, 0.8);
    --toolbar-padding: var(--spacing-sm) var(--spacing-md);
    --toolbar-height: 48px;

    /* === 性能优化设置 === */
    /* 字体渲染优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* 硬件加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* === 暗黑主题颜色覆盖 === */
body.dark-theme {
    /* 主色调保持一致，只调整亮度 */
    --color-primary: #60a5fa;
    --color-primary-hover: #3b82f6;
    --color-primary-active: #2563eb;
    
    --color-success: #34d399;
    --color-warning: #fbbf24;
    --color-error: #f87171;
    --color-danger: #f87171;
    --color-danger-hover: #ef4444;
    --color-danger-active: #dc2626;
    --color-danger-dark: #b91c1c;
    --color-danger-light: rgba(248, 113, 113, 0.1);
    --color-info: #22d3ee;

    /* 背景色 */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-surface: #1e293b;
    --bg-elevated: #334155;
    --bg-overlay: rgba(15, 23, 42, 0.95);
    --bg-code: #1a1e23;
    --bg-hover: rgba(59, 130, 246, 0.1);
    --bg-active: rgba(59, 130, 246, 0.2);
    --bg-selected: rgba(59, 130, 246, 0.15);

    /* 文字色 */
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-muted: #94a3b8;
    --text-inverse: #0f172a;
    --text-link: #60a5fa;
    --text-link-hover: #93c5fd;
    --text-code: #e6edf3;

    /* 边框色 */
    --border-primary: #334155;
    --border-secondary: #475569;
    --border-tertiary: #64748b;
    --border-focus: #60a5fa;

    /* 灰色系统 - 暗黑主题 */
    --color-gray-50: #1e293b;
    --color-gray-100: #334155;
    --color-gray-200: #475569;
    --color-gray-300: #64748b;
    --color-gray-400: #94a3b8;
    --color-gray-500: #cbd5e1;
    --color-gray-600: #e2e8f0;
    --color-gray-700: #f1f5f9;
    --color-gray-800: #f8fafc;
    --color-gray-900: #ffffff;

    /* 组件特定变量覆盖 */
    --input-bg: rgba(51, 65, 85, 0.5);
    --input-border: rgba(71, 85, 105, 0.3);
    --toolbar-bg: rgba(51, 65, 85, 0.8);

    /* === 按钮悬停效果系统 - 暗黑主题专用 === */
    /* 现代极简风格 - 高对比度悬停背景色，确保与白色文字有足够对比度 */
    --button-hover-bg: rgba(100, 116, 139, 0.9);           /* 更深的灰色，对比度更好 */
    --button-hover-bg-light: rgba(71, 85, 105, 0.7);       /* 轻量悬停效果 */
    --button-hover-bg-strong: rgba(51, 65, 85, 0.95);      /* 强烈悬停效果 */

    /* 特殊按钮悬停背景色 */
    --menu-button-hover-bg: rgba(71, 85, 105, 0.9);        /* 汉堡菜单按钮 */
    --sidebar-button-hover-bg: rgba(100, 116, 139, 0.8);   /* 侧边栏按钮 */
    --toolbar-button-hover-bg: rgba(100, 116, 139, 0.85);  /* 工具栏按钮 */

    /* 悬停状态文字色 - 确保高对比度 */
    --button-hover-text: #ffffff;                           /* 纯白色文字 */
    --button-hover-text-muted: #f1f5f9;                   /* 稍微柔和的白色 */

    /* 聚焦效果变量 - 暗黑主题 */
    --focus-border-color: #60a5fa;                         /* 明亮的蓝色聚焦边框 */
    --focus-glow-color: rgba(96, 165, 250, 0.25);          /* 蓝色发光效果 */
    --focus-glow-radius: 2px;                              /* 聚焦发光半径 */
    --focus-transition: all 250ms cubic-bezier(0, 0, 0.2, 1); /* 聚焦过渡动画 */

    /* 阴影系统调整 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4), 0 2px 4px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.4), 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.4), 0 10px 10px rgba(0, 0, 0, 0.2);
    --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.6);
    --shadow-button: 0 4px 12px rgba(59, 130, 246, 0.4), 0 2px 6px rgba(59, 130, 246, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    --shadow-button-hover: 0 8px 20px rgba(59, 130, 246, 0.5), 0 4px 12px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.25);
    --shadow-button-danger: 0 4px 12px rgba(239, 68, 68, 0.4), 0 2px 6px rgba(239, 68, 68, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    --shadow-button-danger-hover: 0 8px 20px rgba(239, 68, 68, 0.5), 0 4px 12px rgba(239, 68, 68, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

/* === 统一按钮悬停效果系统 === */

/* === 白天主题按钮悬停效果 === */

/* 顶部工具栏按钮悬停效果 - 白天主题 */
.top-action-button:hover {
    color: var(--text-primary) !important;
    background-color: rgba(243, 244, 246, 0.8) !important;
    border-color: rgba(209, 213, 219, 0.6) !important;
    box-shadow: var(--shadow-sm) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
}

.top-action-button:active {
    background-color: rgba(229, 231, 235, 0.9) !important;
    border-color: rgba(156, 163, 175, 0.8) !important;
    transform: scale(0.98) !important;
    box-shadow: var(--shadow-xs) !important;
}

/* 汉堡菜单按钮悬停效果已迁移到统一样式系统 */

/* 汉堡菜单线条悬停效果 - 白天主题 */
#menu-toggle-button:hover span {
    background-color: var(--text-primary) !important;
}

/* 汉堡菜单按钮聚焦效果 - 移除蓝色聚焦框，保持无障碍性 */
#menu-toggle-button:focus,
button#menu-toggle-button:focus,
body #menu-toggle-button:focus {
    outline: none !important;
    box-shadow: none !important;
    border: 1px solid transparent !important;
}

#menu-toggle-button:focus-visible,
button#menu-toggle-button:focus-visible,
body #menu-toggle-button:focus-visible {
    outline: 2px solid var(--focus-border-color) !important;
    outline-offset: 2px !important;
    border-radius: 6px !important;
    box-shadow: none !important;
}

/* 输入区域工具栏按钮悬停效果 - 白天主题 */
.input-action-button:hover,
.toolbar-button:hover {
    color: var(--text-primary) !important;
    background: linear-gradient(135deg,
        rgba(248, 250, 252, 0.9) 0%,
        rgba(241, 245, 249, 0.95) 100%) !important;
    border-color: rgba(209, 213, 219, 0.8) !important;
    box-shadow: var(--shadow-sm) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
}

.input-action-button:active,
.toolbar-button:active {
    background: linear-gradient(135deg,
        rgba(229, 231, 235, 0.9) 0%,
        rgba(209, 213, 219, 0.85) 100%) !important;
    box-shadow: var(--shadow-xs) !important;
    transform: scale(0.98) !important;
}

/* 侧边栏关闭按钮悬停效果 - 白天主题 */
#sidebar-close-button:hover {
    color: var(--text-primary) !important;
    background: linear-gradient(135deg,
        rgba(243, 244, 246, 0.8) 0%,
        rgba(229, 231, 235, 0.9) 100%) !important;
    border-color: rgba(209, 213, 219, 0.6) !important;
    box-shadow: var(--shadow-sm) !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
}

#sidebar-close-button:active {
    background: linear-gradient(135deg,
        rgba(229, 231, 235, 0.9) 0%,
        rgba(209, 213, 219, 0.85) 100%) !important;
    box-shadow: var(--shadow-xs) !important;
    transform: scale(0.98) !important;
}

/* 侧边栏删除按钮悬停效果 - 白天主题 */
.session-delete-button:hover {
    color: #ffffff !important;
    background-color: var(--color-danger) !important;
    border-color: var(--color-danger) !important;
    transform: translateY(-50%) scale(1.1) !important;
    box-shadow: var(--shadow-md) !important;
    opacity: 1 !important;
    transition: all var(--duration-fast) var(--ease-out) !important;
}

/* === 暗黑主题按钮悬停效果覆盖 === */
/* 现代极简风格 - 使用颜色变化和轻微阴影，避免过度的变换效果 */
body.dark-theme {
    /* === 1. 顶部工具栏按钮悬停效果 === */
    .top-action-button:hover {
        color: var(--button-hover-text) !important;
        background: linear-gradient(135deg,
            var(--button-hover-bg) 0%,
            rgba(100, 116, 139, 0.95) 100%) !important;
        border-color: var(--border-secondary) !important;
        box-shadow:
            var(--shadow-sm),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        transition: all var(--duration-fast) var(--ease-out) !important;
    }

    .top-action-button:active {
        background: linear-gradient(135deg,
            rgba(51, 65, 85, 0.95) 0%,
            rgba(71, 85, 105, 0.9) 100%) !important;
        box-shadow:
            inset 0 2px 4px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
        transform: scale(0.98) !important;
    }

    /* === 2. 底部输入区域工具栏按钮悬停效果 === */
    .input-action-button:hover,
    .toolbar-button:hover {
        background: linear-gradient(135deg,
            var(--toolbar-button-hover-bg) 0%,
            rgba(100, 116, 139, 0.9) 100%) !important;
        border-color: var(--color-primary) !important;
        color: var(--button-hover-text) !important;
        box-shadow:
            var(--shadow-sm),
            inset 0 1px 0 rgba(255, 255, 255, 0.08) !important;
        transition: all var(--duration-fast) var(--ease-out) !important;
    }

    .input-action-button:active,
    .toolbar-button:active {
        background: linear-gradient(135deg,
            rgba(51, 65, 85, 0.9) 0%,
            rgba(71, 85, 105, 0.85) 100%) !important;
        box-shadow:
            inset 0 2px 4px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.03) !important;
        transform: scale(0.98) !important;
    }

    /* === 3. 侧边栏删除按钮悬停效果 - 暗黑主题优化 === */
    .session-delete-button:hover {
        color: #ffffff !important;  /* 强制使用纯白色确保最高对比度 */
        background-color: var(--color-danger) !important;
        border-color: var(--color-danger) !important;
        transform: translateY(-50%) scale(1.1) !important;
        box-shadow: var(--shadow-sm) !important;
        opacity: 1 !important;
        /* 添加文字阴影增强可读性 */
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    }

    /* === 4. 侧边栏关闭按钮悬停效果 - 现代极简风格 === */
    #sidebar-close-button:hover {
        color: var(--button-hover-text) !important;
        background: linear-gradient(135deg,
            var(--sidebar-button-hover-bg) 0%,
            rgba(100, 116, 139, 0.9) 100%) !important;
        border-color: var(--border-secondary) !important;
        /* 移除位移动效，使用轻微阴影 */
        box-shadow:
            var(--shadow-sm),
            inset 0 1px 0 rgba(255, 255, 255, 0.08) !important;
        transition: all var(--duration-fast) var(--ease-out) !important;
    }

    #sidebar-close-button:active {
        background: linear-gradient(135deg,
            rgba(51, 65, 85, 0.9) 0%,
            rgba(71, 85, 105, 0.85) 100%) !important;
        box-shadow:
            inset 0 2px 4px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.03) !important;
        transform: scale(0.98) !important;
    }

    /* === 5. 汉堡菜单按钮统一样式系统 === */

    /* 汉堡菜单按钮CSS变量 */
    :root {
        --menu-button-bg: transparent;
        --menu-button-color: var(--text-secondary);
        --menu-button-border: transparent;
        --menu-button-hover-bg: rgba(243, 244, 246, 0.8);
        --menu-button-hover-color: var(--text-primary);
        --menu-button-hover-border: rgba(209, 213, 219, 0.6);
        --menu-button-active-bg: rgba(229, 231, 235, 0.9);
        --menu-button-active-border: rgba(156, 163, 175, 0.8);
        --menu-button-transition: all 200ms cubic-bezier(0.2, 0, 0.2, 1);
    }

    /* 暗色主题变量 */
    body.dark-theme {
        --menu-button-bg: transparent;
        --menu-button-color: var(--text-secondary);
        --menu-button-border: transparent;
        --menu-button-hover-bg: linear-gradient(135deg, rgba(71, 85, 105, 0.9) 0%, rgba(71, 85, 105, 0.95) 100%);
        --menu-button-hover-color: #e2e8f0;
        --menu-button-hover-border: rgba(100, 116, 139, 0.5);
        --menu-button-active-bg: linear-gradient(135deg, rgba(51, 65, 85, 0.9) 0%, rgba(71, 85, 105, 0.85) 100%);
        --menu-button-active-border: rgba(100, 116, 139, 0.7);
    }

    /* 基础样式 */
    #menu-toggle-button {
        background: var(--menu-button-bg);
        color: var(--menu-button-color);
        border: 1px solid var(--menu-button-border);
        transition: var(--menu-button-transition);
    }

    /* 悬停效果 */
    #menu-toggle-button:hover {
        background: var(--menu-button-hover-bg);
        color: var(--menu-button-hover-color);
        border-color: var(--menu-button-hover-border);
        box-shadow: var(--shadow-sm);
    }

    /* 激活效果 */
    #menu-toggle-button:active {
        background: var(--menu-button-active-bg);
        border-color: var(--menu-button-active-border);
        transform: scale(0.98);
        box-shadow: var(--shadow-xs);
    }

    /* 线条样式 */
    #menu-toggle-button span {
        background-color: currentColor;
        transition: var(--menu-button-transition);
    }

    /* 聚焦效果 */
    #menu-toggle-button:focus {
        outline: none;
        box-shadow: none;
        border-color: transparent;
    }

    #menu-toggle-button:focus-visible {
        outline: 2px solid var(--focus-border-color);
        outline-offset: 2px;
        border-radius: 6px;
        box-shadow: none;
    }

    /* 主题切换过渡状态 */
    .theme-transitioning #menu-toggle-button {
        transition: var(--menu-button-transition);
    }

    /* 确保过渡期间样式稳定 */
    .theme-transitioning #menu-toggle-button span {
        transition: var(--menu-button-transition);
    }
}

/* === 全局重置和基础样式 === */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    margin: 0;
    padding: 0;
    transition: background-color var(--duration-normal) var(--ease-out),
                color var(--duration-normal) var(--ease-out);
}

/* === 聚焦状态统一样式 === */

/* 绘画界面输入框聚焦效果 - 确保与全局一致 */
.image-gen-input:focus,
.image-gen-textarea:focus,
#image-gen-modal input:focus,
#image-gen-modal textarea:focus {
    outline: none !important;
    border-color: var(--focus-border-color) !important;
    border-width: 1.5px !important;
    /* 统一的聚焦阴影效果 */
    box-shadow:
        0 0 0 2px rgba(100, 181, 246, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.08) !important;
    transition: var(--focus-transition) !important;
    /* 边角渲染优化 */
    border-style: solid !important;
    box-sizing: border-box !important;
    /* 强制硬件加速，优化边角渲染 */
    transform: translateZ(0);
    /* 抗锯齿优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 边框渲染优化 */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
}

/* 暗黑主题下的绘画界面聚焦效果 */
body.dark-theme .image-gen-input:focus,
body.dark-theme .image-gen-textarea:focus,
body.dark-theme #image-gen-modal input:focus,
body.dark-theme #image-gen-modal textarea:focus {
    border-color: var(--focus-border-color) !important;
    box-shadow:
        0 0 0 2px var(--focus-glow-color),
        0 2px 8px rgba(0, 0, 0, 0.12) !important;
}
/* 全局聚焦样式已移除，使用特定选择器管理聚焦效果 */

/* === 滚动条统一样式 === */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: var(--radius-sm);
    transition: background var(--duration-fast) var(--ease-out);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-tertiary);
}

/* Firefox */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--border-secondary) var(--bg-secondary);
}
