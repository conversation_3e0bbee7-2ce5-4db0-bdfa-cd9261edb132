/* ===================================================================
   统一主题过渡系统 - 解决主题切换不同步和生硬问题
   ================================================================= */

/* === 全局过渡基础设置 === */
:root {
    /* 确保所有主题相关的过渡都使用统一的时间和缓动 */
    --theme-transition: var(--theme-transition-duration) var(--theme-transition-easing);
    --theme-transition-with-delay: var(--theme-transition-duration) var(--theme-transition-easing) var(--theme-transition-delay);
}

/* === 核心元素过渡 === */
body {
    /* 统一背景和文本颜色过渡 */
    transition: 
        background-color var(--theme-transition),
        background-image var(--theme-transition),
        color var(--theme-transition);
    
    /* 启用GPU加速 */
    will-change: background-color, color;
}

/* 过渡状态管理 */
body.theme-transitioning {
    /* 过渡期间禁用用户选择，避免视觉干扰 */
    user-select: none;
    pointer-events: none;
}

body.theme-transitioning * {
    /* 确保所有子元素都参与过渡 */
    transition-duration: var(--theme-transition-duration) !important;
    transition-timing-function: var(--theme-transition-easing) !important;
}

/* === 按钮组件过渡 === */
.btn,
.top-action-button,
.sidebar-button,
button {
    transition: 
        background-color var(--theme-transition),
        background-image var(--theme-transition),
        border-color var(--theme-transition),
        color var(--theme-transition),
        box-shadow var(--theme-transition),
        transform var(--duration-fast) var(--ease-out);
    
    will-change: background-color, border-color, color, box-shadow;
}

/* === 输入框组件过渡 === */
.input,
.textarea,
input,
textarea,
select {
    transition: 
        background-color var(--theme-transition),
        border-color var(--theme-transition),
        color var(--theme-transition),
        box-shadow var(--theme-transition);
    
    will-change: background-color, border-color, color, box-shadow;
}

/* === 卡片和容器过渡 === */
.card,
.modal__content,
.dropdown__menu,
.message-bubble,
#sidebar,
#top-bar,
#input-container {
    transition: 
        background-color var(--theme-transition),
        background-image var(--theme-transition),
        border-color var(--theme-transition),
        box-shadow var(--theme-transition);
    
    will-change: background-color, border-color, box-shadow;
}

/* === 文本元素过渡 === */
h1, h2, h3, h4, h5, h6,
p, span, div, a,
.text-primary,
.text-secondary,
.text-tertiary {
    transition: color var(--theme-transition);
    will-change: color;
}

/* === 边框和分割线过渡 === */
hr,
.border,
.border-top,
.border-bottom,
.border-left,
.border-right {
    transition: border-color var(--theme-transition);
    will-change: border-color;
}

/* === 滚动条过渡 === */
::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
    transition: background-color var(--theme-transition);
    will-change: background-color;
}

/* === 特殊组件过渡优化 === */

/* 代码块 */
.code-block,
pre,
code {
    transition: 
        background-color var(--theme-transition),
        border-color var(--theme-transition),
        color var(--theme-transition);
    
    will-change: background-color, border-color, color;
}

/* 图片容器 */
.image-display-frame,
.batch-image-item,
.image-preview-frame {
    transition: 
        background-color var(--theme-transition),
        background-image var(--theme-transition),
        border-color var(--theme-transition),
        box-shadow var(--theme-transition);
    
    will-change: background-color, border-color, box-shadow;
}

/* 提示优化栏 */
.prompt-optimization-bar,
.prompt-optimization-input,
.prompt-optimization-send {
    transition: 
        background-color var(--theme-transition),
        background-image var(--theme-transition),
        border-color var(--theme-transition),
        color var(--theme-transition),
        box-shadow var(--theme-transition);
    
    will-change: background-color, border-color, color, box-shadow;
}

/* === 过渡状态视觉反馈 === */
body.theme-transitioning::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: var(--z-modal);
    pointer-events: none;
    opacity: 0;
    animation: themeTransitionOverlay var(--theme-transition-duration) var(--theme-transition-easing);
}

@keyframes themeTransitionOverlay {
    0% { opacity: 0; }
    50% { opacity: 0.02; }
    100% { opacity: 0; }
}

/* === 性能优化 === */

/* 为频繁变化的元素启用GPU加速 */
.theme-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 减少重排重绘的优化 */
.theme-transitioning .animate-fade-in,
.theme-transitioning .animate-fade-out,
.theme-transitioning .animate-slide-up,
.theme-transitioning .animate-slide-down {
    animation-play-state: paused;
}

/* === 响应式过渡优化 === */
@media (max-width: 768px) {
    /* 移动端减少过渡时间，提升响应速度 */
    :root {
        --theme-transition-duration: 300ms;
    }
}

/* === 用户偏好支持 === */
@media (prefers-reduced-motion: reduce) {
    /* 尊重用户的减少动画偏好 */
    :root {
        --theme-transition-duration: 0.01ms;
        --theme-transition-delay: 0ms;
    }
    
    body.theme-transitioning::before {
        display: none;
    }
}

/* === 高对比度模式优化 === */
@media (prefers-contrast: high) {
    /* 高对比度模式下加快过渡速度 */
    :root {
        --theme-transition-duration: 200ms;
    }
}

/* === 调试模式 === */
.theme-debug body.theme-transitioning * {
    /* 调试模式下显示过渡边框 */
    outline: 1px dashed rgba(255, 0, 0, 0.3) !important;
}
