/* ===================================================================
   优化后的主题系统 - 消除!important滥用
   ================================================================= */

/* === 基础主题样式 === */
body {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--duration-normal) var(--ease-out),
                color var(--duration-normal) var(--ease-out);
}

/* === 暗黑主题特定样式 === */
body.dark-theme {
    /* 主题特定的背景渐变 */
    background-image:
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(96, 165, 250, 0.01) 0%, transparent 50%);
}

/* === 按钮主题样式 === */
body.dark-theme .btn--primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
    border-color: var(--color-primary);
    box-shadow: var(--shadow-sm), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

body.dark-theme .btn--primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--color-primary-hover) 0%, #1d4ed8 100%);
    box-shadow: var(--shadow-md), inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

body.dark-theme .btn--secondary {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-sm), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

body.dark-theme .btn--secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, #64748b 100%);
    box-shadow: var(--shadow-md), inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

/* === 输入框主题样式 === */
body.dark-theme .input,
body.dark-theme .textarea {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-secondary);
    color: var(--text-primary);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

body.dark-theme .input:focus,
body.dark-theme .textarea:focus {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, #64748b 100%);
    border-color: var(--focus-border-color);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color),
                inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* === 卡片主题样式 === */
body.dark-theme .card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-color: var(--border-primary);
    box-shadow: var(--shadow-lg), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

body.dark-theme .card__header,
body.dark-theme .card__footer {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-secondary);
}

/* === 模态框主题样式 === */
body.dark-theme .modal__content {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-color: var(--border-primary);
    box-shadow: var(--shadow-2xl), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

body.dark-theme .modal__header {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-secondary);
}

/* === 下拉菜单主题样式 === */
body.dark-theme .dropdown__menu {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-color: var(--border-primary);
    box-shadow: var(--shadow-xl), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

body.dark-theme .dropdown__item:hover,
body.dark-theme .dropdown__item:focus {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

/* === 滚动条主题样式 === */
body.dark-theme ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

body.dark-theme ::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-base);
}

body.dark-theme ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, #64748b 100%);
    border-radius: var(--radius-base);
    border: 2px solid transparent;
    background-clip: padding-box;
}

body.dark-theme ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #64748b 0%, #475569 100%);
}

body.dark-theme ::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #475569 0%, #334155 100%);
}

/* Firefox滚动条 */
body.dark-theme * {
    scrollbar-width: thin;
    scrollbar-color: var(--bg-tertiary) var(--bg-secondary);
}

/* === 特殊组件主题样式 === */

/* 侧边栏 */
body.dark-theme #sidebar {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-color: var(--border-primary);
    box-shadow: var(--shadow-xl);
}

/* 顶部栏 */
body.dark-theme #top-bar {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-color: var(--border-primary);
    backdrop-filter: blur(10px);
}

/* 消息气泡 */
body.dark-theme .message-bubble {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-secondary);
    box-shadow: var(--shadow-sm), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

body.dark-theme .message-bubble.user {
    background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
    color: var(--color-white);
}

body.dark-theme .message-bubble.ai {
    background: linear-gradient(135deg, var(--color-success) 0%, #059669 100%);
    color: var(--color-white);
}

/* 代码块 */
body.dark-theme .code-block {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: var(--border-secondary);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 输入区域 */
body.dark-theme #input-container {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-color: var(--border-primary);
    backdrop-filter: blur(10px);
}

/* === 状态样式 === */
body.dark-theme .is-loading::after {
    border-color: var(--color-primary);
    border-top-color: transparent;
}

body.dark-theme .is-active {
    background: linear-gradient(135deg, var(--color-primary-light) 0%, rgba(59, 130, 246, 0.1) 100%);
    color: var(--color-primary);
}

/* === 动画优化 === */
body.dark-theme .animate-fade-in,
body.dark-theme .animate-fade-out,
body.dark-theme .animate-slide-up,
body.dark-theme .animate-slide-down {
    animation-timing-function: var(--ease-out);
}

/* === 响应式主题调整 === */
@media (max-width: 768px) {
    body.dark-theme #sidebar {
        background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
        box-shadow: var(--shadow-2xl);
    }
    
    body.dark-theme #top-bar {
        background: var(--bg-primary);
        backdrop-filter: blur(10px);
    }
}

@media (max-width: 480px) {
    body.dark-theme #sidebar.open::before {
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(3px);
    }
}

/* === 高对比度模式支持 === */
@media (prefers-contrast: high) {
    body.dark-theme {
        --border-primary: var(--color-gray-400);
        --border-secondary: var(--color-gray-300);
        --text-secondary: var(--color-gray-200);
    }
}

/* === 减少动画偏好支持 === */
@media (prefers-reduced-motion: reduce) {
    body.dark-theme * {
        animation-duration: 0.01ms;
        animation-iteration-count: 1;
        transition-duration: 0.01ms;
    }
}

/* === 打印样式 === */
@media print {
    body.dark-theme {
        background: white;
        color: black;
    }
    
    body.dark-theme .card,
    body.dark-theme .modal__content,
    body.dark-theme .dropdown__menu {
        background: white;
        border-color: #ccc;
        box-shadow: none;
    }
    
    body.dark-theme .btn {
        background: white;
        color: black;
        border-color: #ccc;
    }
}
