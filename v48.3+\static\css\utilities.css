/* ===================================================================
   CSS工具类系统 - 原子化CSS
   ================================================================= */

/* === 显示工具类 === */
.u-hidden { display: none; }
.u-block { display: block; }
.u-inline { display: inline; }
.u-inline-block { display: inline-block; }
.u-flex { display: flex; }
.u-inline-flex { display: inline-flex; }
.u-grid { display: grid; }

/* === 可见性工具类 === */
.u-visible { visibility: visible; }
.u-invisible { visibility: hidden; }
.u-opacity-0 { opacity: 0; }
.u-opacity-50 { opacity: 0.5; }
.u-opacity-100 { opacity: 1; }

/* === 位置工具类 === */
.u-static { position: static; }
.u-relative { position: relative; }
.u-absolute { position: absolute; }
.u-fixed { position: fixed; }
.u-sticky { position: sticky; }

/* === Flexbox工具类 === */
.u-flex-row { flex-direction: row; }
.u-flex-col { flex-direction: column; }
.u-flex-wrap { flex-wrap: wrap; }
.u-flex-nowrap { flex-wrap: nowrap; }

.u-justify-start { justify-content: flex-start; }
.u-justify-center { justify-content: center; }
.u-justify-end { justify-content: flex-end; }
.u-justify-between { justify-content: space-between; }
.u-justify-around { justify-content: space-around; }

.u-items-start { align-items: flex-start; }
.u-items-center { align-items: center; }
.u-items-end { align-items: flex-end; }
.u-items-stretch { align-items: stretch; }

.u-flex-1 { flex: 1; }
.u-flex-auto { flex: auto; }
.u-flex-none { flex: none; }

/* === 间距工具类 === */
.u-m-0 { margin: 0; }
.u-m-xs { margin: var(--spacing-xs); }
.u-m-sm { margin: var(--spacing-sm); }
.u-m-md { margin: var(--spacing-md); }
.u-m-lg { margin: var(--spacing-lg); }
.u-m-xl { margin: var(--spacing-xl); }

.u-mt-0 { margin-top: 0; }
.u-mt-xs { margin-top: var(--spacing-xs); }
.u-mt-sm { margin-top: var(--spacing-sm); }
.u-mt-md { margin-top: var(--spacing-md); }
.u-mt-lg { margin-top: var(--spacing-lg); }
.u-mt-xl { margin-top: var(--spacing-xl); }

.u-mr-0 { margin-right: 0; }
.u-mr-xs { margin-right: var(--spacing-xs); }
.u-mr-sm { margin-right: var(--spacing-sm); }
.u-mr-md { margin-right: var(--spacing-md); }
.u-mr-lg { margin-right: var(--spacing-lg); }
.u-mr-xl { margin-right: var(--spacing-xl); }

.u-mb-0 { margin-bottom: 0; }
.u-mb-xs { margin-bottom: var(--spacing-xs); }
.u-mb-sm { margin-bottom: var(--spacing-sm); }
.u-mb-md { margin-bottom: var(--spacing-md); }
.u-mb-lg { margin-bottom: var(--spacing-lg); }
.u-mb-xl { margin-bottom: var(--spacing-xl); }

.u-ml-0 { margin-left: 0; }
.u-ml-xs { margin-left: var(--spacing-xs); }
.u-ml-sm { margin-left: var(--spacing-sm); }
.u-ml-md { margin-left: var(--spacing-md); }
.u-ml-lg { margin-left: var(--spacing-lg); }
.u-ml-xl { margin-left: var(--spacing-xl); }

.u-p-0 { padding: 0; }
.u-p-xs { padding: var(--spacing-xs); }
.u-p-sm { padding: var(--spacing-sm); }
.u-p-md { padding: var(--spacing-md); }
.u-p-lg { padding: var(--spacing-lg); }
.u-p-xl { padding: var(--spacing-xl); }

.u-pt-0 { padding-top: 0; }
.u-pt-xs { padding-top: var(--spacing-xs); }
.u-pt-sm { padding-top: var(--spacing-sm); }
.u-pt-md { padding-top: var(--spacing-md); }
.u-pt-lg { padding-top: var(--spacing-lg); }
.u-pt-xl { padding-top: var(--spacing-xl); }

.u-pr-0 { padding-right: 0; }
.u-pr-xs { padding-right: var(--spacing-xs); }
.u-pr-sm { padding-right: var(--spacing-sm); }
.u-pr-md { padding-right: var(--spacing-md); }
.u-pr-lg { padding-right: var(--spacing-lg); }
.u-pr-xl { padding-right: var(--spacing-xl); }

.u-pb-0 { padding-bottom: 0; }
.u-pb-xs { padding-bottom: var(--spacing-xs); }
.u-pb-sm { padding-bottom: var(--spacing-sm); }
.u-pb-md { padding-bottom: var(--spacing-md); }
.u-pb-lg { padding-bottom: var(--spacing-lg); }
.u-pb-xl { padding-bottom: var(--spacing-xl); }

.u-pl-0 { padding-left: 0; }
.u-pl-xs { padding-left: var(--spacing-xs); }
.u-pl-sm { padding-left: var(--spacing-sm); }
.u-pl-md { padding-left: var(--spacing-md); }
.u-pl-lg { padding-left: var(--spacing-lg); }
.u-pl-xl { padding-left: var(--spacing-xl); }

/* === 文本工具类 === */
.u-text-left { text-align: left; }
.u-text-center { text-align: center; }
.u-text-right { text-align: right; }
.u-text-justify { text-align: justify; }

.u-text-xs { font-size: var(--font-size-xs); }
.u-text-sm { font-size: var(--font-size-sm); }
.u-text-base { font-size: var(--font-size-base); }
.u-text-lg { font-size: var(--font-size-lg); }
.u-text-xl { font-size: var(--font-size-xl); }
.u-text-2xl { font-size: var(--font-size-2xl); }

.u-font-light { font-weight: var(--font-weight-light); }
.u-font-normal { font-weight: var(--font-weight-normal); }
.u-font-medium { font-weight: var(--font-weight-medium); }
.u-font-semibold { font-weight: var(--font-weight-semibold); }
.u-font-bold { font-weight: var(--font-weight-bold); }

.u-leading-tight { line-height: var(--line-height-tight); }
.u-leading-normal { line-height: var(--line-height-normal); }
.u-leading-relaxed { line-height: var(--line-height-relaxed); }

.u-text-primary { color: var(--text-primary); }
.u-text-secondary { color: var(--text-secondary); }
.u-text-tertiary { color: var(--text-tertiary); }
.u-text-muted { color: var(--text-muted); }
.u-text-inverse { color: var(--text-inverse); }

.u-text-success { color: var(--color-success); }
.u-text-warning { color: var(--color-warning); }
.u-text-danger { color: var(--color-danger); }
.u-text-info { color: var(--color-info); }

/* === 背景工具类 === */
.u-bg-primary { background-color: var(--bg-primary); }
.u-bg-secondary { background-color: var(--bg-secondary); }
.u-bg-tertiary { background-color: var(--bg-tertiary); }

.u-bg-success { background-color: var(--color-success); }
.u-bg-warning { background-color: var(--color-warning); }
.u-bg-danger { background-color: var(--color-danger); }
.u-bg-info { background-color: var(--color-info); }

.u-bg-transparent { background-color: transparent; }

/* === 边框工具类 === */
.u-border { border: 1px solid var(--border-primary); }
.u-border-0 { border: 0; }
.u-border-t { border-top: 1px solid var(--border-primary); }
.u-border-r { border-right: 1px solid var(--border-primary); }
.u-border-b { border-bottom: 1px solid var(--border-primary); }
.u-border-l { border-left: 1px solid var(--border-primary); }

.u-border-primary { border-color: var(--border-primary); }
.u-border-secondary { border-color: var(--border-secondary); }
.u-border-tertiary { border-color: var(--border-tertiary); }
.u-border-focus { border-color: var(--border-focus); }
.u-border-danger { border-color: var(--border-danger); }

.u-rounded-none { border-radius: var(--radius-none); }
.u-rounded-sm { border-radius: var(--radius-sm); }
.u-rounded { border-radius: var(--radius-base); }
.u-rounded-md { border-radius: var(--radius-md); }
.u-rounded-lg { border-radius: var(--radius-lg); }
.u-rounded-xl { border-radius: var(--radius-xl); }
.u-rounded-2xl { border-radius: var(--radius-2xl); }
.u-rounded-full { border-radius: var(--radius-full); }

/* === 阴影工具类 === */
.u-shadow-none { box-shadow: none; }
.u-shadow-xs { box-shadow: var(--shadow-xs); }
.u-shadow-sm { box-shadow: var(--shadow-sm); }
.u-shadow { box-shadow: var(--shadow-base); }
.u-shadow-md { box-shadow: var(--shadow-md); }
.u-shadow-lg { box-shadow: var(--shadow-lg); }
.u-shadow-xl { box-shadow: var(--shadow-xl); }
.u-shadow-2xl { box-shadow: var(--shadow-2xl); }
.u-shadow-inner { box-shadow: var(--shadow-inner); }

/* === 变换工具类 === */
.u-transform { transform: translateZ(0); }
.u-scale-95 { transform: scale(0.95); }
.u-scale-100 { transform: scale(1); }
.u-scale-105 { transform: scale(1.05); }
.u-scale-110 { transform: scale(1.1); }

.u-rotate-0 { transform: rotate(0deg); }
.u-rotate-90 { transform: rotate(90deg); }
.u-rotate-180 { transform: rotate(180deg); }
.u-rotate-270 { transform: rotate(270deg); }

/* === 过渡工具类 === */
.u-transition-none { transition: none; }
.u-transition-all { transition: all var(--duration-normal) var(--ease-out); }
.u-transition-fast { transition: all var(--duration-fast) var(--ease-out); }
.u-transition-slow { transition: all var(--duration-slow) var(--ease-out); }

/* === 光标工具类 === */
.u-cursor-auto { cursor: auto; }
.u-cursor-default { cursor: default; }
.u-cursor-pointer { cursor: pointer; }
.u-cursor-wait { cursor: wait; }
.u-cursor-text { cursor: text; }
.u-cursor-move { cursor: move; }
.u-cursor-not-allowed { cursor: not-allowed; }

/* === 溢出工具类 === */
.u-overflow-auto { overflow: auto; }
.u-overflow-hidden { overflow: hidden; }
.u-overflow-visible { overflow: visible; }
.u-overflow-scroll { overflow: scroll; }

.u-overflow-x-auto { overflow-x: auto; }
.u-overflow-x-hidden { overflow-x: hidden; }
.u-overflow-x-scroll { overflow-x: scroll; }

.u-overflow-y-auto { overflow-y: auto; }
.u-overflow-y-hidden { overflow-y: hidden; }
.u-overflow-y-scroll { overflow-y: scroll; }

/* === Z-index工具类 === */
.u-z-0 { z-index: var(--z-base); }
.u-z-10 { z-index: 10; }
.u-z-20 { z-index: 20; }
.u-z-30 { z-index: 30; }
.u-z-40 { z-index: 40; }
.u-z-50 { z-index: 50; }
.u-z-dropdown { z-index: var(--z-dropdown); }
.u-z-modal { z-index: var(--z-modal); }
.u-z-tooltip { z-index: var(--z-tooltip); }

/* === 交互状态工具类 === */
.u-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.u-loading {
    position: relative;
    pointer-events: none;
}

.u-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid var(--color-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* === 聚焦工具类 === */
.u-focus-ring:focus {
    outline: none;
    border-color: var(--focus-border-color);
    box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
    transition: var(--focus-transition);
}

/* === 可访问性工具类 === */
.u-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.u-sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    padding: inherit;
    margin: inherit;
    overflow: visible;
    clip: auto;
    white-space: normal;
}

/* === 响应式工具类 === */
@media (min-width: 640px) {
    .sm\:u-block { display: block; }
    .sm\:u-hidden { display: none; }
    .sm\:u-flex { display: flex; }
}

@media (min-width: 768px) {
    .md\:u-block { display: block; }
    .md\:u-hidden { display: none; }
    .md\:u-flex { display: flex; }
}

@media (min-width: 1024px) {
    .lg\:u-block { display: block; }
    .lg\:u-hidden { display: none; }
    .lg\:u-flex { display: flex; }
}
