/* ===================================================================
   统一CSS变量系统 - 设计系统核心
   ================================================================= */

:root {
    /* === 颜色系统 === */
    
    /* 主色调 */
    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;
    --color-primary-active: #1d4ed8;
    --color-primary-light: #dbeafe;
    --color-primary-dark: #1e40af;
    
    /* 次要色调 */
    --color-secondary: #6b7280;
    --color-secondary-hover: #4b5563;
    --color-secondary-active: #374151;
    --color-secondary-light: #f3f4f6;
    --color-secondary-dark: #1f2937;
    
    /* 功能色彩 */
    --color-success: #10b981;
    --color-success-hover: #059669;
    --color-success-light: #d1fae5;
    --color-warning: #f59e0b;
    --color-warning-hover: #d97706;
    --color-warning-light: #fef3c7;
    --color-danger: #ef4444;
    --color-danger-hover: #dc2626;
    --color-danger-light: #fee2e2;
    --color-info: #06b6d4;
    --color-info-hover: #0891b2;
    --color-info-light: #cffafe;
    
    /* 中性色调 */
    --color-white: #ffffff;
    --color-black: #000000;
    --color-gray-50: #f9fafb;
    --color-gray-100: #f3f4f6;
    --color-gray-200: #e5e7eb;
    --color-gray-300: #d1d5db;
    --color-gray-400: #9ca3af;
    --color-gray-500: #6b7280;
    --color-gray-600: #4b5563;
    --color-gray-700: #374151;
    --color-gray-800: #1f2937;
    --color-gray-900: #111827;
    
    /* === 语义化颜色 === */
    
    /* 背景色 */
    --bg-primary: var(--color-white);
    --bg-secondary: var(--color-gray-50);
    --bg-tertiary: var(--color-gray-100);
    --bg-overlay: rgba(0, 0, 0, 0.5);
    --bg-glass: rgba(255, 255, 255, 0.8);
    
    /* 文本色 */
    --text-primary: var(--color-gray-900);
    --text-secondary: var(--color-gray-600);
    --text-tertiary: var(--color-gray-400);
    --text-muted: var(--color-gray-500);
    --text-inverse: var(--color-white);
    
    /* 边框色 */
    --border-primary: var(--color-gray-200);
    --border-secondary: var(--color-gray-300);
    --border-tertiary: var(--color-gray-400);
    --border-focus: var(--color-primary);
    --border-danger: var(--color-danger);
    
    /* === 间距系统 === */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-2xl: 3rem;      /* 48px */
    --spacing-3xl: 4rem;      /* 64px */
    
    /* === 字体系统 === */
    
    /* 字体族 */
    --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, Monaco, monospace;
    --font-family-serif: Georgia, 'Times New Roman', Times, serif;
    
    /* 字体大小 */
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-base: 1rem;    /* 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.25rem;   /* 20px */
    --font-size-2xl: 1.5rem;   /* 24px */
    --font-size-3xl: 1.875rem; /* 30px */
    --font-size-4xl: 2.25rem;  /* 36px */
    
    /* 字重 */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 行高 */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* === 圆角系统 === */
    --radius-none: 0;
    --radius-sm: 0.125rem;     /* 2px */
    --radius-base: 0.25rem;    /* 4px */
    --radius-md: 0.375rem;     /* 6px */
    --radius-lg: 0.5rem;       /* 8px */
    --radius-xl: 0.75rem;      /* 12px */
    --radius-2xl: 1rem;        /* 16px */
    --radius-full: 9999px;
    
    /* === 阴影系统 === */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-2xl: 0 50px 100px -20px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    
    /* === 动画系统 === */

    /* 持续时间 */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;

    /* 主题切换专用时间 */
    --theme-transition-duration: 400ms;
    --theme-transition-delay: 0ms;

    /* 缓动函数 */
    --ease-linear: linear;
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

    /* 主题切换专用缓动 */
    --theme-transition-easing: cubic-bezier(0.4, 0, 0.2, 1);
    
    /* === Z-index 层级 === */
    --z-base: 0;
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* === 聚焦系统 === */
    --focus-border-color: var(--color-primary);
    --focus-glow-color: rgba(59, 130, 246, 0.25);
    --focus-glow-radius: 3px;
    --focus-transition: all var(--duration-fast) var(--ease-out);
    
    /* === 断点系统 === */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* === 暗黑主题变量覆盖 === */
body.dark-theme {
    /* 背景色 */
    --bg-primary: #1e293b;
    --bg-secondary: #334155;
    --bg-tertiary: #475569;
    --bg-overlay: rgba(0, 0, 0, 0.7);
    --bg-glass: rgba(30, 41, 59, 0.8);
    
    /* 文本色 */
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-muted: #64748b;
    --text-inverse: var(--color-gray-900);
    
    /* 边框色 */
    --border-primary: #475569;
    --border-secondary: #64748b;
    --border-tertiary: #94a3b8;
    --border-focus: #81c784;
    
    /* 聚焦系统 - 暗黑主题 */
    --focus-border-color: #81c784;
    --focus-glow-color: rgba(129, 199, 132, 0.35);
    
    /* 阴影系统 - 暗黑主题 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.15);
    --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.25);
    --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
    --shadow-2xl: 0 50px 100px -20px rgba(0, 0, 0, 0.5);
}

/* === 高对比度模式支持 === */
@media (prefers-contrast: high) {
    :root {
        --border-primary: var(--color-gray-400);
        --border-secondary: var(--color-gray-500);
        --text-secondary: var(--color-gray-700);
    }
    
    body.dark-theme {
        --border-primary: var(--color-gray-400);
        --border-secondary: var(--color-gray-300);
        --text-secondary: var(--color-gray-200);
    }
}

/* === 减少动画偏好支持 === */
@media (prefers-reduced-motion: reduce) {
    :root {
        --duration-fast: 0.01ms;
        --duration-normal: 0.01ms;
        --duration-slow: 0.01ms;
    }
}
