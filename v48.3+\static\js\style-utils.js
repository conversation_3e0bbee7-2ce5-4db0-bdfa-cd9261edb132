/* ===================================================================
   样式工具类 - 替代内联样式操作
   ================================================================= */

/**
 * 样式工具类 - 提供统一的样式操作方法
 */
class StyleUtils {
    /**
     * 添加CSS类
     * @param {Element|string} element - DOM元素或选择器
     * @param {string|string[]} classes - 要添加的类名
     */
    static addClass(element, classes) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        const classArray = Array.isArray(classes) ? classes : [classes];
        el.classList.add(...classArray);
    }

    /**
     * 移除CSS类
     * @param {Element|string} element - DOM元素或选择器
     * @param {string|string[]} classes - 要移除的类名
     */
    static removeClass(element, classes) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        const classArray = Array.isArray(classes) ? classes : [classes];
        el.classList.remove(...classArray);
    }

    /**
     * 切换CSS类
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} className - 要切换的类名
     * @param {boolean} force - 强制添加或移除
     */
    static toggleClass(element, className, force) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        return el.classList.toggle(className, force);
    }

    /**
     * 检查是否包含CSS类
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} className - 要检查的类名
     */
    static hasClass(element, className) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return false;
        
        return el.classList.contains(className);
    }

    /**
     * 显示元素
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} display - 显示类型 (block, flex, inline等)
     */
    static show(element, display = 'block') {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        el.classList.remove('u-hidden');
        if (display === 'flex') {
            el.classList.add('u-flex');
        } else if (display === 'inline') {
            el.classList.add('u-inline');
        } else if (display === 'inline-block') {
            el.classList.add('u-inline-block');
        } else {
            el.classList.add('u-block');
        }
    }

    /**
     * 隐藏元素
     * @param {Element|string} element - DOM元素或选择器
     */
    static hide(element) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        el.classList.remove('u-block', 'u-flex', 'u-inline', 'u-inline-block');
        el.classList.add('u-hidden');
    }

    /**
     * 设置元素为加载状态
     * @param {Element|string} element - DOM元素或选择器
     * @param {boolean} loading - 是否为加载状态
     */
    static setLoading(element, loading = true) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        if (loading) {
            el.classList.add('u-loading');
            el.setAttribute('aria-busy', 'true');
        } else {
            el.classList.remove('u-loading');
            el.removeAttribute('aria-busy');
        }
    }

    /**
     * 设置元素为禁用状态
     * @param {Element|string} element - DOM元素或选择器
     * @param {boolean} disabled - 是否禁用
     */
    static setDisabled(element, disabled = true) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        if (disabled) {
            el.classList.add('u-disabled');
            el.setAttribute('disabled', 'disabled');
            el.setAttribute('aria-disabled', 'true');
        } else {
            el.classList.remove('u-disabled');
            el.removeAttribute('disabled');
            el.removeAttribute('aria-disabled');
        }
    }

    /**
     * 设置元素透明度
     * @param {Element|string} element - DOM元素或选择器
     * @param {number} opacity - 透明度值 (0-1)
     */
    static setOpacity(element, opacity) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        // 移除现有的透明度类
        el.classList.remove('u-opacity-0', 'u-opacity-50', 'u-opacity-100');
        
        if (opacity <= 0) {
            el.classList.add('u-opacity-0');
        } else if (opacity <= 0.5) {
            el.classList.add('u-opacity-50');
        } else {
            el.classList.add('u-opacity-100');
        }
    }

    /**
     * 设置元素变换
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} transform - 变换类型 (scale-95, scale-100, scale-105等)
     */
    static setTransform(element, transform) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        // 移除现有的变换类
        el.classList.remove('u-scale-95', 'u-scale-100', 'u-scale-105', 'u-scale-110');
        
        if (transform) {
            el.classList.add(`u-${transform}`);
        }
    }

    /**
     * 添加动画类
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} animation - 动画类型
     * @param {function} callback - 动画完成回调
     */
    static animate(element, animation, callback) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        const animationClass = `animate-${animation}`;
        el.classList.add(animationClass);
        
        const handleAnimationEnd = () => {
            el.classList.remove(animationClass);
            el.removeEventListener('animationend', handleAnimationEnd);
            if (callback) callback();
        };
        
        el.addEventListener('animationend', handleAnimationEnd);
    }

    /**
     * 设置进度条宽度
     * @param {Element|string} element - 进度条元素或选择器
     * @param {number} percentage - 进度百分比 (0-100)
     */
    static setProgress(element, percentage) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        // 使用CSS自定义属性而不是内联样式
        el.style.setProperty('--progress-width', `${Math.max(0, Math.min(100, percentage))}%`);
    }

    /**
     * 设置元素尺寸
     * @param {Element|string} element - DOM元素或选择器
     * @param {Object} dimensions - 尺寸对象 {width, height}
     */
    static setDimensions(element, dimensions) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        if (dimensions.width !== undefined) {
            el.style.setProperty('--element-width', 
                typeof dimensions.width === 'number' ? `${dimensions.width}px` : dimensions.width);
        }
        
        if (dimensions.height !== undefined) {
            el.style.setProperty('--element-height', 
                typeof dimensions.height === 'number' ? `${dimensions.height}px` : dimensions.height);
        }
    }

    /**
     * 设置元素位置
     * @param {Element|string} element - DOM元素或选择器
     * @param {Object} position - 位置对象 {top, right, bottom, left}
     */
    static setPosition(element, position) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        Object.keys(position).forEach(key => {
            if (position[key] !== undefined) {
                const value = typeof position[key] === 'number' ? `${position[key]}px` : position[key];
                el.style.setProperty(`--element-${key}`, value);
            }
        });
    }

    /**
     * 批量操作元素
     * @param {string} selector - 选择器
     * @param {function} operation - 操作函数
     */
    static batch(selector, operation) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(operation);
    }

    /**
     * 安全地设置CSS自定义属性
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} property - CSS属性名
     * @param {string} value - 属性值
     */
    static setCustomProperty(element, property, value) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        try {
            el.style.setProperty(property, value);
        } catch (error) {
            console.warn(`Failed to set CSS property ${property}:`, error);
        }
    }

    /**
     * 移除CSS自定义属性
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} property - CSS属性名
     */
    static removeCustomProperty(element, property) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        try {
            el.style.removeProperty(property);
        } catch (error) {
            console.warn(`Failed to remove CSS property ${property}:`, error);
        }
    }

    /**
     * 获取计算后的样式值
     * @param {Element|string} element - DOM元素或选择器
     * @param {string} property - CSS属性名
     */
    static getComputedStyle(element, property) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return null;
        
        return window.getComputedStyle(el).getPropertyValue(property);
    }

    /**
     * 检查元素是否可见
     * @param {Element|string} element - DOM元素或选择器
     */
    static isVisible(element) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return false;
        
        return !el.classList.contains('u-hidden') && 
               el.style.display !== 'none' && 
               el.style.visibility !== 'hidden';
    }

    /**
     * 平滑滚动到元素
     * @param {Element|string} element - DOM元素或选择器
     * @param {Object} options - 滚动选项
     */
    static scrollToElement(element, options = {}) {
        const el = typeof element === 'string' ? document.querySelector(element) : element;
        if (!el) return;
        
        el.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
            ...options
        });
    }
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StyleUtils;
} else if (typeof window !== 'undefined') {
    window.StyleUtils = StyleUtils;
}
