/**
 * 统一主题管理器 - 优化版
 * 解决主题切换不同步、过渡生硬、视觉延迟等问题
 *
 * @version 3.0.0
 * <AUTHOR> Team
 */
class ThemeManager {
    constructor() {
        // 过渡状态管理
        this.isTransitioning = false;
        this.transitionDuration = 400; // 与CSS变量同步
        this.pendingTheme = null;
        this.transitionCallbacks = [];
        this.transitionPromise = null;

        // 主题状态管理
        this.currentTheme = 'light';
        this.storageKey = 'theme';
        this.systemThemeQuery = null;

        // DOM元素缓存
        this.elements = {
            body: document.body,
            darkThemeLink: null,
            themeToggleButton: null,
            themeTransitionLink: null
        };

        // 性能优化
        this.debounceTimer = null;
        this.lastTransitionTime = 0;
        this.rafId = null;
        this.batchedUpdates = [];

        // 调试模式
        this.debugMode = false;

        // 初始化
        this.init();
    }

    /**
     * 初始化主题管理器
     */
    init() {
        this.cacheElements();
        this.loadThemePreference();
        this.setupEventListeners();
        this.setupSystemThemeDetection();
        this.preloadThemeResources();
        console.log('[ThemeManager] Initialized successfully');
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements.darkThemeLink = document.getElementById('dark-theme-style');
        this.elements.themeToggleButton = document.getElementById('theme-toggle-button');
        this.elements.themeTransitionLink = document.getElementById('theme-transition-style');

        // 如果主题过渡CSS文件不存在，动态创建
        if (!this.elements.themeTransitionLink) {
            this.createThemeTransitionLink();
        }
    }

    /**
     * 创建主题过渡CSS链接
     */
    createThemeTransitionLink() {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'static/css/theme-transitions.css';
        link.id = 'theme-transition-style';
        document.head.appendChild(link);
        this.elements.themeTransitionLink = link;
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听页面可见性变化，优化性能
        document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));

        // 监听窗口焦点变化
        window.addEventListener('focus', this.handleWindowFocus.bind(this));
        window.addEventListener('blur', this.handleWindowBlur.bind(this));
    }

    /**
     * 设置系统主题检测
     */
    setupSystemThemeDetection() {
        if (window.matchMedia) {
            this.systemThemeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            this.systemThemeQuery.addListener(this.handleSystemThemeChange.bind(this));
        }
    }

    /**
     * 处理系统主题变化
     */
    handleSystemThemeChange(e) {
        console.log('[ThemeManager] System theme changed:', e.matches ? 'dark' : 'light');
        // 可以在这里添加自动跟随系统主题的逻辑
        // if (this.shouldFollowSystemTheme) {
        //     this.switchTheme(e.matches ? 'dark' : 'light');
        // }
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden && this.isTransitioning) {
            // 页面隐藏时暂停过渡
            this.pauseTransition();
        } else if (!document.hidden && this.isTransitioning) {
            // 页面显示时恢复过渡
            this.resumeTransition();
        }
    }

    /**
     * 处理窗口焦点变化
     */
    handleWindowFocus() {
        // 窗口获得焦点时确保主题状态正确
        this.validateThemeState();
    }

    handleWindowBlur() {
        // 窗口失去焦点时可以进行一些清理工作
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
        }
    }

    /**
     * 加载主题偏好设置
     */
    loadThemePreference() {
        try {
            const savedTheme = localStorage.getItem(this.storageKey) || 'light';
            this.currentTheme = savedTheme;
            this.applyThemeImmediate(savedTheme);
            console.log(`[ThemeManager] Loaded theme preference: ${savedTheme}`);
        } catch (error) {
            console.warn('[ThemeManager] Failed to load theme preference:', error);
            this.currentTheme = 'light';
        }
    }

    /**
     * 保存主题偏好设置
     */
    saveThemePreference(theme) {
        try {
            localStorage.setItem(this.storageKey, theme);
            console.log(`[ThemeManager] Saved theme preference: ${theme}`);
        } catch (error) {
            console.warn('[ThemeManager] Failed to save theme preference:', error);
        }
    }

    /**
     * 立即应用主题（无过渡效果）
     * @param {string} theme - 主题名称
     */
    applyThemeImmediate(theme) {
        this.updateDOMClasses(theme);
        this.updateThemeToggleButton(theme);
        this.currentTheme = theme;
    }

    /**
     * 执行主题切换（带过渡效果）- 优化版
     * @param {string} newTheme - 新主题名称
     * @param {Function} callback - 切换完成回调
     * @returns {Promise} 过渡完成的Promise
     */
    async switchTheme(newTheme, callback) {
        // 防重复切换和性能优化
        const now = Date.now();
        if (this.isTransitioning || (now - this.lastTransitionTime) < 100) {
            this.pendingTheme = newTheme;
            if (callback) this.transitionCallbacks.push(callback);
            return this.transitionPromise;
        }

        // 如果主题相同，直接返回
        if (this.currentTheme === newTheme) {
            if (callback) callback();
            return Promise.resolve();
        }

        this.isTransitioning = true;
        this.lastTransitionTime = now;

        // 创建过渡Promise
        this.transitionPromise = this.performThemeTransition(newTheme, callback);

        return this.transitionPromise;
    }

    /**
     * 执行主题过渡的核心逻辑
     */
    async performThemeTransition(newTheme, callback) {
        console.log(`[ThemeManager] Starting optimized transition: ${this.currentTheme} → ${newTheme}`);

        try {
            // 1. 预处理阶段
            await this.preProcessTransition(newTheme);

            // 2. 准备过渡状态
            await this.prepareTransition();

            // 3. 批量应用主题变化
            await this.batchApplyThemeChanges(newTheme);

            // 4. 等待过渡完成
            await this.waitForTransitionComplete();

            // 5. 后处理阶段
            await this.postProcessTransition(newTheme);

            // 6. 清理过渡状态
            await this.cleanupTransition();

            // 7. 保存主题偏好
            this.saveThemePreference(newTheme);

            // 8. 执行回调
            if (callback) callback();
            this.executeCallbacks();

            console.log(`[ThemeManager] Optimized transition completed: ${newTheme}`);

        } catch (error) {
            console.error('[ThemeManager] Transition failed:', error);
            // 错误恢复：回滚到之前的主题
            await this.rollbackTheme();
        } finally {
            this.isTransitioning = false;
            this.transitionPromise = null;

            // 处理待处理的主题切换
            if (this.pendingTheme && this.pendingTheme !== newTheme) {
                const nextTheme = this.pendingTheme;
                this.pendingTheme = null;
                // 使用requestAnimationFrame确保平滑切换
                this.rafId = requestAnimationFrame(() => {
                    this.switchTheme(nextTheme);
                });
            }
        }
    }

    /**
     * 预处理过渡阶段
     */
    async preProcessTransition(newTheme) {
        return new Promise(resolve => {
            // 预加载主题相关资源
            this.preloadThemeAssets(newTheme);

            // 优化性能：暂停不必要的动画
            this.pauseNonEssentialAnimations();

            resolve();
        });
    }

    /**
     * 准备过渡状态 - 优化版
     */
    async prepareTransition() {
        return new Promise(resolve => {
            // 批量DOM操作，减少重排重绘
            this.rafId = requestAnimationFrame(() => {
                // 添加过渡状态类
                document.body.classList.add('theme-transitioning');

                // 为关键元素添加优化类
                this.addOptimizationClasses();

                // 确保DOM更新完成
                requestAnimationFrame(() => {
                    resolve();
                });
            });
        });
    }

    /**
     * 批量应用主题变化
     */
    async batchApplyThemeChanges(theme) {
        return new Promise(resolve => {
            // 使用DocumentFragment批量更新DOM
            const fragment = document.createDocumentFragment();

            // 批量收集所有需要更新的操作
            this.batchedUpdates = [];

            this.rafId = requestAnimationFrame(() => {
                // 更新DOM类名
                this.updateDOMClasses(theme);

                // 更新主题切换按钮
                this.updateThemeToggleButton(theme);

                // 应用批量更新
                this.applyBatchedUpdates();

                // 更新当前主题
                this.currentTheme = theme;

                resolve();
            });
        });
    }

    /**
     * 后处理过渡阶段
     */
    async postProcessTransition(newTheme) {
        return new Promise(resolve => {
            // 恢复暂停的动画
            this.resumeNonEssentialAnimations();

            // 触发主题变化事件
            this.dispatchThemeChangeEvent(newTheme);

            resolve();
        });
    }

    /**
     * 应用主题变化
     */
    async applyThemeChanges(theme) {
        return new Promise(resolve => {
            this.updateDOMClasses(theme);
            this.updateThemeToggleButton(theme);
            this.currentTheme = theme;
            resolve();
        });
    }

    /**
     * 更新DOM类名
     */
    updateDOMClasses(theme) {
        if (theme === 'dark') {
            this.elements.body.classList.add('dark-theme');
            if (this.elements.darkThemeLink) {
                this.elements.darkThemeLink.removeAttribute('disabled');
            }
        } else {
            this.elements.body.classList.remove('dark-theme');
            if (this.elements.darkThemeLink) {
                this.elements.darkThemeLink.setAttribute('disabled', 'true');
            }
        }
    }

    /**
     * 更新主题切换按钮
     */
    updateThemeToggleButton(theme) {
        if (this.elements.themeToggleButton) {
            const icon = theme === 'dark' ? 'fa-moon' : 'fa-sun';
            this.elements.themeToggleButton.innerHTML = `<i class="fas ${icon}"></i>`;
        }
    }

    /**
     * 等待过渡完成 - 优化版
     */
    async waitForTransitionComplete() {
        return new Promise(resolve => {
            // 使用transitionend事件而不是固定时间
            const handleTransitionEnd = (event) => {
                if (event.target === document.body &&
                    (event.propertyName === 'background-color' || event.propertyName === 'color')) {
                    document.body.removeEventListener('transitionend', handleTransitionEnd);
                    resolve();
                }
            };

            document.body.addEventListener('transitionend', handleTransitionEnd);

            // 备用超时机制，防止事件未触发
            setTimeout(() => {
                document.body.removeEventListener('transitionend', handleTransitionEnd);
                resolve();
            }, this.transitionDuration + 100);
        });
    }

    /**
     * 清理过渡状态 - 优化版
     */
    async cleanupTransition() {
        return new Promise(resolve => {
            this.rafId = requestAnimationFrame(() => {
                // 移除过渡状态类
                document.body.classList.remove('theme-transitioning');

                // 移除优化类
                this.removeOptimizationClasses();

                // 清理批量更新队列
                this.batchedUpdates = [];

                resolve();
            });
        });
    }

    /**
     * 回滚主题
     */
    async rollbackTheme() {
        console.warn('[ThemeManager] Rolling back theme due to error');
        try {
            await this.applyThemeImmediate(this.currentTheme);
            await this.cleanupTransition();
        } catch (rollbackError) {
            console.error('[ThemeManager] Rollback failed:', rollbackError);
        }
    }

    /**
     * 执行所有回调
     */
    executeCallbacks() {
        while (this.transitionCallbacks.length > 0) {
            const callback = this.transitionCallbacks.shift();
            try {
                callback();
            } catch (error) {
                console.error('[ThemeTransition] Callback execution failed:', error);
            }
        }
    }

    /**
     * 检查是否正在过渡
     */
    isInTransition() {
        return this.isTransitioning;
    }

    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * 切换主题（在当前主题和另一个主题之间切换）
     */
    toggleTheme(callback) {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        return this.switchTheme(newTheme, callback);
    }

    /**
     * 检查是否为暗黑主题
     */
    isDarkTheme() {
        return this.currentTheme === 'dark';
    }

    /**
     * 检查是否为明亮主题
     */
    isLightTheme() {
        return this.currentTheme === 'light';
    }

    /**
     * 添加过渡完成监听器
     */
    onTransitionComplete(callback) {
        if (this.isTransitioning) {
            this.transitionCallbacks.push(callback);
        } else {
            callback();
        }
    }

    /**
     * 获取主题管理器状态信息
     */
    getStatus() {
        return {
            currentTheme: this.currentTheme,
            isTransitioning: this.isTransitioning,
            pendingTheme: this.pendingTheme,
            transitionDuration: this.transitionDuration,
            lastTransitionTime: this.lastTransitionTime
        };
    }

    /**
     * 预加载主题资源
     */
    preloadThemeResources() {
        // 预加载暗黑主题CSS
        if (!this.elements.darkThemeLink) {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'style';
            link.href = 'static/css/dark_theme.css';
            document.head.appendChild(link);
        }
    }

    /**
     * 预加载主题相关资源
     */
    preloadThemeAssets(theme) {
        // 可以在这里预加载主题相关的图片、字体等资源
        if (theme === 'dark') {
            // 预加载暗黑主题相关资源
        }
    }

    /**
     * 添加优化类
     */
    addOptimizationClasses() {
        const elements = [
            document.body,
            ...document.querySelectorAll('.card, .btn, .input, .message-bubble')
        ];

        elements.forEach(el => {
            if (el) el.classList.add('theme-optimized');
        });
    }

    /**
     * 移除优化类
     */
    removeOptimizationClasses() {
        const elements = document.querySelectorAll('.theme-optimized');
        elements.forEach(el => el.classList.remove('theme-optimized'));
    }

    /**
     * 暂停非必要动画
     */
    pauseNonEssentialAnimations() {
        const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-fade-out, .animate-slide-up, .animate-slide-down');
        animatedElements.forEach(el => {
            el.style.animationPlayState = 'paused';
        });
    }

    /**
     * 恢复非必要动画
     */
    resumeNonEssentialAnimations() {
        const animatedElements = document.querySelectorAll('.animate-fade-in, .animate-fade-out, .animate-slide-up, .animate-slide-down');
        animatedElements.forEach(el => {
            el.style.animationPlayState = 'running';
        });
    }

    /**
     * 应用批量更新
     */
    applyBatchedUpdates() {
        this.batchedUpdates.forEach(update => {
            try {
                update();
            } catch (error) {
                console.warn('[ThemeManager] Batched update failed:', error);
            }
        });
    }

    /**
     * 触发主题变化事件
     */
    dispatchThemeChangeEvent(newTheme) {
        const event = new CustomEvent('themechange', {
            detail: {
                oldTheme: this.currentTheme,
                newTheme: newTheme,
                timestamp: Date.now()
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * 验证主题状态
     */
    validateThemeState() {
        const bodyHasDarkClass = document.body.classList.contains('dark-theme');
        const expectedDarkClass = this.currentTheme === 'dark';

        if (bodyHasDarkClass !== expectedDarkClass) {
            console.warn('[ThemeManager] Theme state mismatch detected, correcting...');
            this.applyThemeImmediate(this.currentTheme);
        }
    }

    /**
     * 暂停过渡
     */
    pauseTransition() {
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
        }
    }

    /**
     * 恢复过渡
     */
    resumeTransition() {
        // 如果有待处理的主题切换，恢复执行
        if (this.pendingTheme) {
            const theme = this.pendingTheme;
            this.pendingTheme = null;
            this.switchTheme(theme);
        }
    }

    /**
     * 启用调试模式
     */
    enableDebugMode() {
        this.debugMode = true;
        document.body.classList.add('theme-debug');
        console.log('[ThemeManager] Debug mode enabled');
    }

    /**
     * 禁用调试模式
     */
    disableDebugMode() {
        this.debugMode = false;
        document.body.classList.remove('theme-debug');
        console.log('[ThemeManager] Debug mode disabled');
    }

    /**
     * 销毁主题管理器（清理资源）- 优化版
     */
    destroy() {
        // 清理定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // 清理动画帧
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
        }

        // 清理事件监听器
        if (this.systemThemeQuery) {
            this.systemThemeQuery.removeListener(this.handleSystemThemeChange.bind(this));
        }

        document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
        window.removeEventListener('focus', this.handleWindowFocus.bind(this));
        window.removeEventListener('blur', this.handleWindowBlur.bind(this));

        // 清理回调和状态
        this.transitionCallbacks = [];
        this.batchedUpdates = [];
        this.transitionPromise = null;

        // 清理DOM引用
        this.elements = {};

        console.log('[ThemeManager] Destroyed successfully');
    }
}

// 创建全局实例
window.ThemeManager = new ThemeManager();

// 向后兼容：保留旧的接口名称
window.ThemeTransitionManager = window.ThemeManager;

// 导出到全局作用域供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
}
