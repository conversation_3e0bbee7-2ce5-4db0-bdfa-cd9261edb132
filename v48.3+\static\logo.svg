<svg width="200" height="220" viewBox="0 0 100 110" xmlns="http://www.w3.org/2000/svg">

  <defs>
    <!-- 1. 新的球体渐变 ("蛋黄蛋清"效果) -->
    <radialGradient id="ballGradient_Egg" cx="50%" cy="48%" r="70%" fx="45%" fy="40%">
      <!-- 高光区 (非常亮) -->
      <stop offset="0%" style="stop-color:#FFFFFF; stop-opacity:1" />
      <stop offset="8%" style="stop-color:#FFFFB0; stop-opacity:1" /> <!-- 亮黄色核心 -->
      <!-- 蛋黄区 (黄色主体) -->
      <stop offset="25%" style="stop-color:#FFD700; stop-opacity:1" />
      <!-- 过渡区 (黄 -> 橙 -> 橙红) -->
      <stop offset="50%" style="stop-color:#FFA500; stop-opacity:1" />
      <stop offset="75%" style="stop-color:#FF6347; stop-opacity:1" /> <!-- Tomato/橙红色 -->
      <!-- 边缘区 (泛红) -->
      <stop offset="100%" style="stop-color:#DC143C; stop-opacity:1" /> <!-- Crimson/深红色边缘 -->
    </radialGradient>

    <!-- 星星内部渐变 (保持) -->
    <radialGradient id="starGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:#FF4040; stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B00000; stop-opacity:1" />
    </radialGradient>

    <!-- 星星形状 (保持) -->
    <path id="starShape_Final" fill="url(#starGradient)"
          d="M 0 -10 L 2.351 -3.09 L 9.511 -3.09 L 3.804 1.18 L 5.878 8.09 L 0 5 L -5.878 8.09 L -3.804 1.18 L -9.511 -3.09 L -2.351 -3.09 Z" />

    <!-- 中心放射光渐变 (色调微调，更偏黄) -->
    <radialGradient id="centerGlowGradient_Yellow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" style="stop-color:#FFFFE0; stop-opacity:0.8"/> <!-- 中心亮黄 -->
      <stop offset="50%" style="stop-color:#FFDA60; stop-opacity:0.3"/> <!-- 调整中间色 -->
      <stop offset="100%" style="stop-color:#FFB000; stop-opacity:0"/> <!-- 外部透明 -->
    </radialGradient>

    <!-- 2. 星星红色光晕滤镜 (更明显，圆形，红色) -->
    <filter id="starHaloFilter_Red" x="-150%" y="-150%" width="400%" height="400%">
      <!-- 增大 stdDeviation 使光晕更大更圆 -->
      <!-- 颜色设为红色，调整透明度 -->
      <feDropShadow dx="0" dy="0" stdDeviation="3.5" flood-color="#FF0000" flood-opacity="0.8" />
    </filter>

    <!-- 球体自身光晕滤镜 (可选保留，或根据新风格调整/移除) -->
    <!-- 这里暂时保留一个柔和的黄色光晕，与中心色呼应 -->
     <filter id="sphereHaloFilter_SoftYellow" x="-50%" y="-50%" width="200%" height="200%">
       <feDropShadow dx="0" dy="0" stdDeviation="4" flood-color="#FFD700" flood-opacity="0.35" />
     </filter>

    <!-- 星星内部模糊滤镜 (保持，用于融合) -->
    <filter id="starBlur" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceGraphic" stdDeviation="0.3" />
    </filter>

    <!-- 底部辉光/阴影 (保持黄色/橙色调) -->
    <radialGradient id="glowGradient_Enhanced" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
        <stop offset="0%" style="stop-color:#FFD700; stop-opacity:0.8"/>
        <stop offset="100%" style="stop-color:#FFA500; stop-opacity:0"/>
    </radialGradient>
    <filter id="softGlow" x="-50%" y="-50%" width="200%" height="200%">
        <feGaussianBlur in="SourceGraphic" stdDeviation="3.5" />
    </filter>
  </defs>

  <!-- 整体缩小的容器 (保持) -->
  <g transform="scale(0.9) translate(5 5.5)">

    <!-- 中心放射光 (使用调整后的黄色渐变) -->
    <circle cx="50" cy="50" r="60" fill="url(#centerGlowGradient_Yellow)" opacity="0.7" />

    <!-- 底部辉光 (保持) -->
    <ellipse cx="50" cy="101" rx="48" ry="17" fill="url(#glowGradient_Enhanced)" filter="url(#softGlow)" />

    <!-- 球体 (使用新的"蛋黄蛋清"渐变，并应用柔和黄色光晕) -->
    <circle cx="50" cy="50" r="49" fill="url(#ballGradient_Egg)" filter="url(#sphereHaloFilter_SoftYellow)" />

    <!-- 星星组 (应用内部模糊和透明度) -->
    <g id="starGroup_WithRedHalos" filter="url(#starBlur)" opacity="0.95">
      <!-- 对每个 <use> 应用新的红色光晕滤镜 -->

      <!-- 星星 1: 中心 -->
      <use href="#starShape_Final" transform="translate(50 50) scale(1.0)" filter="url(#starHaloFilter_Red)"/>
      <!-- 星星 2: 上 (压缩) -->
      <use href="#starShape_Final" transform="translate(50 18) scale(0.98 0.83)" filter="url(#starHaloFilter_Red)"/>
      <!-- 星星 3: 右上 -->
      <use href="#starShape_Final" transform="translate(78 34) scale(0.96)" filter="url(#starHaloFilter_Red)"/>
      <!-- 星星 4: 右下 -->
      <use href="#starShape_Final" transform="translate(78 66) scale(0.96)" filter="url(#starHaloFilter_Red)"/>
      <!-- 星星 5: 下 (压缩) -->
      <use href="#starShape_Final" transform="translate(50 82) scale(0.98 0.83)" filter="url(#starHaloFilter_Red)"/>
      <!-- 星星 6: 左下 -->
      <use href="#starShape_Final" transform="translate(22 66) scale(0.96)" filter="url(#starHaloFilter_Red)"/>
      <!-- 星星 7: 左上 -->
      <use href="#starShape_Final" transform="translate(22 34) scale(0.96)" filter="url(#starHaloFilter_Red)"/>
    </g>

  </g> <!-- 结束整体缩小容器 -->

</svg>