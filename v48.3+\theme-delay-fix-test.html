<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题延迟组件修复测试</title>
    <link rel="stylesheet" href="static/css/variables.css">
    <link rel="stylesheet" href="static/css/design-system.css">
    <link rel="stylesheet" href="static/css/theme-transitions.css">
    <link rel="stylesheet" href="static/css/dark_theme.css" id="dark-theme-style" disabled>
    <style>
        body {
            font-family: var(--font-family-base);
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .test-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .theme-toggle-btn {
            background: var(--color-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--duration-fast) var(--ease-out);
        }

        .theme-toggle-btn:hover {
            background: var(--color-primary-hover);
        }

        /* 测试组件1: 消息界面输入区域 */
        .input-test-area {
            background: var(--bg-overlay);
            backdrop-filter: var(--blur-md);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            margin-bottom: 15px;
        }

        .input-wrapper-test {
            background: var(--input-bg);
            backdrop-filter: var(--blur-md);
            border: 1px solid var(--input-border);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-sm);
            transition: border-color var(--duration-fast) var(--ease-out),
                        box-shadow var(--duration-fast) var(--ease-out);
            position: relative;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .message-input-test {
            background: transparent;
            border: none;
            color: var(--text-primary);
            padding: 11.25px 15px;
            outline: none;
            resize: none;
            min-height: 44px;
            font-family: var(--font-family-base);
            transition: all var(--duration-fast) var(--ease-out);
        }

        .message-input-test::placeholder {
            color: var(--text-muted);
            opacity: 0.8;
            font-style: italic;
        }

        /* 测试组件2: Mermaid图形背景容器 */
        .mermaid-test-container {
            position: relative;
            margin: var(--mermaid-container-margin);
        }

        .mermaid-diagram-test {
            text-align: center;
            background: linear-gradient(135deg,
                var(--bg-secondary) 0%,
                var(--bg-tertiary) 100%);
            color: var(--text-secondary);
            padding: var(--mermaid-diagram-padding);
            border-radius: var(--mermaid-diagram-border-radius);
            border: 1px solid var(--border-primary);
            min-height: var(--mermaid-diagram-min-height);
            display: block;
            overflow: auto;
            transition: all var(--duration-normal) var(--ease-out);
            box-shadow: var(--shadow-sm);
        }

        /* 测试组件3: 侧边栏搜索框 */
        .sidebar-test-area {
            background: linear-gradient(135deg,
                var(--bg-elevated) 0%,
                var(--bg-tertiary) 100%);
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            padding: 20px;
        }

        .search-input-test {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 36px;
            border: 1px solid var(--border-secondary);
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg,
                var(--bg-elevated) 0%,
                var(--bg-tertiary) 100%);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            transition: all var(--duration-fast) var(--ease-out);
            box-sizing: border-box;
        }

        .search-input-test:focus {
            outline: none;
            border-color: var(--focus-border-color);
            box-shadow: 0 0 0 var(--focus-glow-radius) var(--focus-glow-color);
            background: var(--bg-hover);
        }

        .search-input-test::placeholder {
            color: var(--text-muted);
            opacity: 0.8;
            font-style: italic;
        }

        .test-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: var(--radius-md);
            font-size: 0.9em;
            font-weight: 500;
        }

        .status-pass {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-success);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .status-fail {
            background: rgba(239, 68, 68, 0.1);
            color: var(--color-error);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .test-log {
            background: var(--bg-code);
            color: var(--text-code);
            padding: 15px;
            border-radius: var(--radius-md);
            font-family: var(--font-family-mono);
            font-size: 0.85em;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-header">
        <h1>主题延迟组件修复测试</h1>
        <button class="theme-toggle-btn" onclick="toggleTheme()">
            <span id="theme-icon">🌙</span> 切换主题
        </button>
    </div>

    <div class="test-container">
        <!-- 测试组件1: 消息界面输入区域 -->
        <div class="test-section">
            <h3>1. 消息界面输入区域</h3>
            <div class="input-test-area" id="input-container">
                <div class="input-wrapper-test" id="input-textarea-wrapper">
                    <textarea class="message-input-test" id="message-input" 
                              placeholder="测试消息输入框主题切换延迟修复..." rows="3"></textarea>
                </div>
            </div>
            <div class="test-status" id="input-status">等待测试...</div>
        </div>

        <!-- 测试组件2: Mermaid图形背景容器 -->
        <div class="test-section">
            <h3>2. Mermaid图形背景容器</h3>
            <div class="mermaid-test-container mermaid-container">
                <div class="mermaid-diagram-test mermaid-diagram-view">
                    <p>模拟Mermaid图表容器</p>
                    <div style="background: var(--bg-primary); padding: 10px; border-radius: 4px; margin: 10px 0;">
                        图表内容区域
                    </div>
                </div>
            </div>
            <div class="test-status" id="mermaid-status">等待测试...</div>
        </div>

        <!-- 测试组件3: 侧边栏搜索框 -->
        <div class="test-section">
            <h3>3. 侧边栏搜索框</h3>
            <div class="sidebar-test-area" id="sidebar-controls">
                <div class="search-input-container">
                    <input type="search" class="search-input-test" id="session-search-input" 
                           placeholder="搜索对话标题...">
                </div>
            </div>
            <div class="test-status" id="sidebar-status">等待测试...</div>
        </div>

        <!-- 测试日志 -->
        <div class="test-section full-width">
            <h3>测试日志</h3>
            <div class="test-log" id="test-log">等待开始测试...\n</div>
        </div>
    </div>

    <script>
        let currentTheme = 'light';
        let testLog = '';

        function log(message) {
            testLog += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            document.getElementById('test-log').textContent = testLog;
            console.log(message);
        }

        function toggleTheme() {
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            log(`开始切换主题: ${currentTheme} -> ${newTheme}`);
            
            // 记录切换前的状态
            recordPreSwitchState();
            
            // 应用主题
            applyTheme(newTheme);
            
            // 延迟检查切换效果
            setTimeout(() => {
                checkThemeSwitchResults(newTheme);
            }, 100);
        }

        function applyTheme(theme) {
            currentTheme = theme;
            const darkThemeLink = document.getElementById('dark-theme-style');
            const themeIcon = document.getElementById('theme-icon');

            if (theme === 'dark') {
                document.body.classList.add('dark-theme');
                if (darkThemeLink) darkThemeLink.removeAttribute('disabled');
                themeIcon.textContent = '🌙';
            } else {
                document.body.classList.remove('dark-theme');
                if (darkThemeLink) darkThemeLink.setAttribute('disabled', 'true');
                themeIcon.textContent = '☀️';
            }

            // 应用延迟组件修复
            forceThemeSyncForDelayedComponents(theme);
            
            log(`主题已应用: ${theme}`);
        }

        function forceThemeSyncForDelayedComponents(theme) {
            log(`强制同步延迟组件主题: ${theme}`);
            
            // 1. 修复消息界面输入区域延迟同步
            const inputContainer = document.getElementById('input-container');
            const inputWrapper = document.getElementById('input-textarea-wrapper');
            const messageInput = document.getElementById('message-input');
            
            [inputContainer, inputWrapper, messageInput].forEach(element => {
                if (element) {
                    element.style.transition = 'none';
                    element.offsetHeight; // 强制重排
                    element.style.transition = '';
                }
            });
            
            // 2. 修复Mermaid图形背景容器延迟同步
            const mermaidContainers = document.querySelectorAll('.mermaid-container, .mermaid-diagram-view');
            mermaidContainers.forEach(container => {
                container.style.transition = 'none';
                container.offsetHeight; // 强制重排
                container.style.transition = '';
            });
            
            // 3. 修复侧边栏搜索框延迟同步
            const sessionSearchInput = document.getElementById('session-search-input');
            const sidebarControls = document.getElementById('sidebar-controls');
            
            [sessionSearchInput, sidebarControls].forEach(element => {
                if (element) {
                    element.style.transition = 'none';
                    element.offsetHeight; // 强制重排
                    element.style.transition = '';
                }
            });
            
            log(`延迟组件主题同步完成`);
        }

        function recordPreSwitchState() {
            log(`记录切换前状态...`);
        }

        function checkThemeSwitchResults(expectedTheme) {
            log(`检查主题切换结果: ${expectedTheme}`);
            
            const hasCorrectBodyClass = expectedTheme === 'dark' ? 
                document.body.classList.contains('dark-theme') : 
                !document.body.classList.contains('dark-theme');
            
            // 检查各组件状态
            const inputStatus = checkComponentTheme('input-container', expectedTheme);
            const mermaidStatus = checkComponentTheme('mermaid-diagram-view', expectedTheme);
            const sidebarStatus = checkComponentTheme('session-search-input', expectedTheme);
            
            // 更新状态显示
            updateStatusDisplay('input-status', inputStatus);
            updateStatusDisplay('mermaid-status', mermaidStatus);
            updateStatusDisplay('sidebar-status', sidebarStatus);
            
            const overallStatus = hasCorrectBodyClass && inputStatus && mermaidStatus && sidebarStatus;
            log(`主题切换${overallStatus ? '成功' : '失败'}: ${expectedTheme}`);
        }

        function checkComponentTheme(elementId, expectedTheme) {
            const element = document.getElementById(elementId);
            if (!element) return false;
            
            const computedStyle = window.getComputedStyle(element);
            const backgroundColor = computedStyle.backgroundColor;
            
            // 简单的颜色检查逻辑
            const isDarkBackground = backgroundColor.includes('rgb') && 
                backgroundColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/) &&
                backgroundColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)[1] < 100;
            
            return expectedTheme === 'dark' ? isDarkBackground : !isDarkBackground;
        }

        function updateStatusDisplay(statusId, passed) {
            const statusElement = document.getElementById(statusId);
            statusElement.className = `test-status ${passed ? 'status-pass' : 'status-fail'}`;
            statusElement.textContent = passed ? '✅ 通过' : '❌ 失败';
        }

        // 初始化
        log('主题延迟组件修复测试页面已加载');
        log('点击"切换主题"按钮开始测试');
    </script>
</body>
</html>
