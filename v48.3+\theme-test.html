<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题切换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            transition: background-color 0.3s ease, color 0.3s ease;
            background-color: #ffffff;
            color: #333333;
        }
        
        body.dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: background 0.3s ease;
        }
        
        body.dark-theme .container {
            background: rgba(0, 0, 0, 0.3);
        }
        
        .theme-toggle {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
            margin-bottom: 20px;
        }
        
        .theme-toggle:hover {
            background: #0056b3;
        }
        
        body.dark-theme .theme-toggle {
            background: #0d6efd;
        }
        
        body.dark-theme .theme-toggle:hover {
            background: #0b5ed7;
        }
        
        .status {
            padding: 15px;
            border-radius: 6px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            margin: 20px 0;
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }
        
        body.dark-theme .status {
            background: #2d3748;
            border-color: #0d6efd;
        }
        
        .test-results {
            margin-top: 20px;
        }
        
        .test-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e9ecef;
            transition: background-color 0.3s ease;
        }
        
        body.dark-theme .test-item {
            background: #374151;
        }
        
        .success {
            border-left: 4px solid #28a745;
        }
        
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>主题切换功能测试</h1>
        
        <button class="theme-toggle" onclick="testThemeToggle()">
            <i class="fas fa-sun"></i> 切换主题
        </button>
        
        <div class="status">
            <h3>当前状态</h3>
            <p><strong>当前主题:</strong> <span id="current-theme">light</span></p>
            <p><strong>localStorage主题:</strong> <span id="stored-theme">未设置</span></p>
            <p><strong>body类名:</strong> <span id="body-classes">无</span></p>
        </div>
        
        <div class="test-results">
            <h3>测试结果</h3>
            <div id="test-output"></div>
        </div>
    </div>

    <script>
        // 模拟main.js中的主题相关变量和函数
        let currentTheme = 'light';
        const THEME_KEY = 'themePreference';
        
        // 模拟applyTheme函数的核心逻辑
        function applyTheme(theme) {
            currentTheme = theme;
            const body = document.body;
            
            if (theme === 'dark') {
                body.classList.add('dark-theme');
            } else {
                body.classList.remove('dark-theme');
            }
            
            // 更新按钮图标
            const button = document.querySelector('.theme-toggle');
            const icon = theme === 'dark' ? 'fa-moon' : 'fa-sun';
            button.innerHTML = `<i class="fas ${icon}"></i> 切换主题`;
            
            // 保存到localStorage
            try {
                localStorage.setItem(THEME_KEY, theme);
            } catch (error) {
                console.warn('Failed to save theme to localStorage:', error);
            }
            
            console.log(`Theme applied: ${theme}`);
            updateStatus();
        }
        
        // 模拟toggleTheme函数的优化版本
        function toggleTheme() {
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(newTheme);
            console.log(`[Theme] Theme toggled to: ${newTheme}`);
        }
        
        // 测试函数
        function testThemeToggle() {
            const testOutput = document.getElementById('test-output');
            const testResults = [];
            
            // 记录切换前状态
            const beforeTheme = currentTheme;
            
            // 执行主题切换
            toggleTheme();
            
            // 验证切换结果
            const afterTheme = currentTheme;
            const bodyHasDarkClass = document.body.classList.contains('dark-theme');
            const storedTheme = localStorage.getItem(THEME_KEY);
            
            // 测试1: 主题变量是否正确切换
            if (beforeTheme !== afterTheme) {
                testResults.push({
                    test: '主题变量切换',
                    result: 'success',
                    message: `从 ${beforeTheme} 切换到 ${afterTheme}`
                });
            } else {
                testResults.push({
                    test: '主题变量切换',
                    result: 'error',
                    message: '主题变量未发生变化'
                });
            }
            
            // 测试2: DOM类名是否正确应用
            const expectedDarkClass = afterTheme === 'dark';
            if (bodyHasDarkClass === expectedDarkClass) {
                testResults.push({
                    test: 'DOM类名应用',
                    result: 'success',
                    message: `body.dark-theme 类名状态正确: ${bodyHasDarkClass}`
                });
            } else {
                testResults.push({
                    test: 'DOM类名应用',
                    result: 'error',
                    message: `body.dark-theme 类名状态错误: 期望 ${expectedDarkClass}, 实际 ${bodyHasDarkClass}`
                });
            }
            
            // 测试3: localStorage是否正确保存
            if (storedTheme === afterTheme) {
                testResults.push({
                    test: 'localStorage保存',
                    result: 'success',
                    message: `主题偏好已保存: ${storedTheme}`
                });
            } else {
                testResults.push({
                    test: 'localStorage保存',
                    result: 'error',
                    message: `localStorage保存失败: 期望 ${afterTheme}, 实际 ${storedTheme}`
                });
            }
            
            // 显示测试结果
            testOutput.innerHTML = testResults.map(result => 
                `<div class="test-item ${result.result}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`
            ).join('');
        }
        
        // 更新状态显示
        function updateStatus() {
            document.getElementById('current-theme').textContent = currentTheme;
            document.getElementById('stored-theme').textContent = localStorage.getItem(THEME_KEY) || '未设置';
            document.getElementById('body-classes').textContent = document.body.className || '无';
        }
        
        // 页面加载时初始化
        function loadThemePreference() {
            const savedTheme = localStorage.getItem(THEME_KEY) || 'light';
            applyTheme(savedTheme);
        }
        
        // 初始化
        loadThemePreference();
        updateStatus();
        
        // 每秒更新状态显示
        setInterval(updateStatus, 1000);
    </script>
</body>
</html>
